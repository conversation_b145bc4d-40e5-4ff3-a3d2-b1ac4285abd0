import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, UserRole, Permission } from '@/lib/auth';

interface AnalyticsData {
  overview: {
    totalUsers: number;
    activeUsers: number;
    totalProjects: number;
    totalRevenue: number;
    conversionRate: number;
    churnRate: number;
  };
  userGrowth: {
    date: string;
    newUsers: number;
    activeUsers: number;
    churnedUsers: number;
  }[];
  revenueGrowth: {
    date: string;
    revenue: number;
    subscriptions: number;
    mrr: number;
  }[];
  projectStats: {
    date: string;
    created: number;
    published: number;
    templates: number;
  }[];
  featureUsage: {
    feature: string;
    usage: number;
    users: number;
    growth: number;
  }[];
  geographics: {
    country: string;
    users: number;
    revenue: number;
    percentage: number;
  }[];
  devices: {
    device: string;
    users: number;
    percentage: number;
  }[];
  topProjects: {
    id: string;
    name: string;
    author: string;
    views: number;
    forks: number;
    stars: number;
  }[];
}

// GET /api/admin/analytics - Get comprehensive analytics data
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(UserRole.ADMIN, [Permission.ADMIN_DASHBOARD])(request);
    
    if (authResult instanceof Response) {
      return authResult;
    }

    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '30d'; // 7d, 30d, 90d, 1y
    const metric = searchParams.get('metric'); // specific metric to fetch

    // Generate mock analytics data
    const mockAnalytics: AnalyticsData = {
      overview: {
        totalUsers: 1247,
        activeUsers: 892,
        totalProjects: 3456,
        totalRevenue: 23400,
        conversionRate: 18.7,
        churnRate: 3.2
      },
      userGrowth: generateUserGrowthData(timeRange),
      revenueGrowth: generateRevenueGrowthData(timeRange),
      projectStats: generateProjectStatsData(timeRange),
      featureUsage: [
        { feature: 'AI Assistant', usage: 15678, users: 234, growth: 25.3 },
        { feature: 'Visual Builder', usage: 8945, users: 156, growth: 18.7 },
        { feature: 'Monaco Editor', usage: 12456, users: 445, growth: 12.1 },
        { feature: 'Project Templates', usage: 6789, users: 289, growth: 31.2 },
        { feature: 'Auto-save', usage: 23456, users: 567, growth: 8.9 },
        { feature: 'Code Sharing', usage: 4567, users: 123, growth: 22.4 }
      ],
      geographics: [
        { country: 'United States', users: 456, revenue: 8920, percentage: 36.6 },
        { country: 'India', users: 234, revenue: 2340, percentage: 18.8 },
        { country: 'United Kingdom', users: 123, revenue: 2460, percentage: 9.9 },
        { country: 'Germany', users: 89, revenue: 1780, percentage: 7.1 },
        { country: 'Canada', users: 67, revenue: 1340, percentage: 5.4 },
        { country: 'Others', users: 278, revenue: 6560, percentage: 22.2 }
      ],
      devices: [
        { device: 'Desktop', users: 789, percentage: 63.3 },
        { device: 'Mobile', users: 345, percentage: 27.7 },
        { device: 'Tablet', users: 113, percentage: 9.0 }
      ],
      topProjects: [
        {
          id: 'proj_1',
          name: 'React Dashboard',
          author: '<EMAIL>',
          views: 1234,
          forks: 45,
          stars: 89
        },
        {
          id: 'proj_2',
          name: 'Vue E-commerce',
          author: '<EMAIL>',
          views: 987,
          forks: 32,
          stars: 67
        },
        {
          id: 'proj_3',
          name: 'Node.js API',
          author: '<EMAIL>',
          views: 756,
          forks: 28,
          stars: 54
        }
      ]
    };

    // If specific metric requested, return only that
    if (metric && metric in mockAnalytics) {
      return NextResponse.json({
        success: true,
        data: {
          [metric]: mockAnalytics[metric as keyof AnalyticsData],
          timeRange,
          lastUpdated: new Date()
        }
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        ...mockAnalytics,
        timeRange,
        lastUpdated: new Date()
      }
    });

  } catch (error) {
    console.error('Error fetching analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics data' },
      { status: 500 }
    );
  }
}

// Generate user growth data based on time range
function generateUserGrowthData(timeRange: string) {
  const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365;
  const data = [];
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    
    data.push({
      date: date.toISOString().split('T')[0],
      newUsers: Math.floor(Math.random() * 20) + 5,
      activeUsers: Math.floor(Math.random() * 100) + 50,
      churnedUsers: Math.floor(Math.random() * 5) + 1
    });
  }
  
  return data;
}

// Generate revenue growth data
function generateRevenueGrowthData(timeRange: string) {
  const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365;
  const data = [];
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    
    const subscriptions = Math.floor(Math.random() * 10) + 2;
    const revenue = subscriptions * 10; // $10 per subscription
    
    data.push({
      date: date.toISOString().split('T')[0],
      revenue,
      subscriptions,
      mrr: revenue // Simplified MRR calculation
    });
  }
  
  return data;
}

// Generate project statistics data
function generateProjectStatsData(timeRange: string) {
  const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365;
  const data = [];
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    
    data.push({
      date: date.toISOString().split('T')[0],
      created: Math.floor(Math.random() * 30) + 10,
      published: Math.floor(Math.random() * 20) + 5,
      templates: Math.floor(Math.random() * 3) + 1
    });
  }
  
  return data;
}

// POST /api/admin/analytics - Track custom analytics event
export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(UserRole.ADMIN, [Permission.ADMIN_DASHBOARD])(request);
    
    if (authResult instanceof Response) {
      return authResult;
    }

    const { event, properties, userId } = await request.json();

    if (!event) {
      return NextResponse.json(
        { error: 'Event name is required' },
        { status: 400 }
      );
    }

    // TODO: Store analytics event in database
    // await storeAnalyticsEvent({
    //   event,
    //   properties,
    //   userId,
    //   timestamp: new Date(),
    //   source: 'admin'
    // });

    return NextResponse.json({
      success: true,
      message: 'Analytics event tracked successfully'
    });

  } catch (error) {
    console.error('Error tracking analytics event:', error);
    return NextResponse.json(
      { error: 'Failed to track analytics event' },
      { status: 500 }
    );
  }
}
