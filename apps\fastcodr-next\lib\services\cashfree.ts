import crypto from 'crypto';
import { CashfreeConfig, CashfreeSubscriptionRequest, CashfreeWebhookEvent } from '@/lib/models/Subscription';

export class CashfreeService {
  private config: CashfreeConfig;
  private baseUrl: string;

  constructor(config: CashfreeConfig) {
    this.config = config;
    this.baseUrl = config.environment === 'production' 
      ? 'https://api.cashfree.com' 
      : 'https://sandbox.cashfree.com';
  }

  // Generate authentication headers
  private getAuthHeaders(): Record<string, string> {
    return {
      'X-Client-Id': this.config.appId,
      'X-Client-Secret': this.config.secretKey,
      'Content-Type': 'application/json',
      'x-api-version': '2023-08-01'
    };
  }

  // Create a customer in Cashfree
  async createCustomer(customerData: {
    customerId: string;
    customerEmail: string;
    customerPhone: string;
    customerName: string;
  }) {
    try {
      const response = await fetch(`${this.baseUrl}/pg/customers`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          customer_id: customerData.customerId,
          customer_email: customerData.customerEmail,
          customer_phone: customerData.customerPhone,
          customer_name: customerData.customerName
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`Cashfree API Error: ${error.message || 'Unknown error'}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating Cashfree customer:', error);
      throw error;
    }
  }

  // Create a subscription plan
  async createSubscriptionPlan(planData: {
    planId: string;
    planName: string;
    planType: 'PERIODIC';
    maxCycles: number;
    amount: number;
    currency: string;
    intervalType: 'MONTH' | 'YEAR';
    intervals: number;
  }) {
    try {
      const response = await fetch(`${this.baseUrl}/pg/plans`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          plan_id: planData.planId,
          plan_name: planData.planName,
          plan_type: planData.planType,
          max_cycles: planData.maxCycles,
          amount: planData.amount,
          currency: planData.currency,
          interval_type: planData.intervalType,
          intervals: planData.intervals
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`Cashfree API Error: ${error.message || 'Unknown error'}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating Cashfree plan:', error);
      throw error;
    }
  }

  // Create a subscription
  async createSubscription(subscriptionData: CashfreeSubscriptionRequest) {
    try {
      const response = await fetch(`${this.baseUrl}/pg/subscriptions`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          subscription_id: subscriptionData.subscriptionId,
          plan_id: subscriptionData.planId,
          customer_id: subscriptionData.customerId,
          customer_email: subscriptionData.customerEmail,
          customer_phone: subscriptionData.customerPhone,
          return_url: subscriptionData.returnUrl,
          notify_url: subscriptionData.notifyUrl,
          subscription_meta: subscriptionData.metadata
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`Cashfree API Error: ${error.message || 'Unknown error'}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating Cashfree subscription:', error);
      throw error;
    }
  }

  // Get subscription details
  async getSubscription(subscriptionId: string) {
    try {
      const response = await fetch(`${this.baseUrl}/pg/subscriptions/${subscriptionId}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`Cashfree API Error: ${error.message || 'Unknown error'}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching Cashfree subscription:', error);
      throw error;
    }
  }

  // Cancel a subscription
  async cancelSubscription(subscriptionId: string) {
    try {
      const response = await fetch(`${this.baseUrl}/pg/subscriptions/${subscriptionId}/cancel`, {
        method: 'POST',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`Cashfree API Error: ${error.message || 'Unknown error'}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error canceling Cashfree subscription:', error);
      throw error;
    }
  }

  // Get subscription payments
  async getSubscriptionPayments(subscriptionId: string) {
    try {
      const response = await fetch(`${this.baseUrl}/pg/subscriptions/${subscriptionId}/payments`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`Cashfree API Error: ${error.message || 'Unknown error'}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching subscription payments:', error);
      throw error;
    }
  }

  // Verify webhook signature
  verifyWebhookSignature(payload: string, signature: string): boolean {
    try {
      const expectedSignature = crypto
        .createHmac('sha256', this.config.webhookSecret)
        .update(payload)
        .digest('hex');

      return crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(expectedSignature)
      );
    } catch (error) {
      console.error('Error verifying webhook signature:', error);
      return false;
    }
  }

  // Process webhook event
  processWebhookEvent(event: CashfreeWebhookEvent): {
    type: string;
    subscriptionId: string;
    status: string;
    data: any;
  } {
    const { eventType, data } = event;

    switch (eventType) {
      case 'SUBSCRIPTION_ACTIVATED':
        return {
          type: 'subscription.activated',
          subscriptionId: data.subscription.subscriptionId,
          status: 'active',
          data: data.subscription
        };

      case 'SUBSCRIPTION_CANCELLED':
        return {
          type: 'subscription.cancelled',
          subscriptionId: data.subscription.subscriptionId,
          status: 'cancelled',
          data: data.subscription
        };

      case 'SUBSCRIPTION_EXPIRED':
        return {
          type: 'subscription.expired',
          subscriptionId: data.subscription.subscriptionId,
          status: 'expired',
          data: data.subscription
        };

      case 'PAYMENT_SUCCESS':
        return {
          type: 'payment.success',
          subscriptionId: data.subscription.subscriptionId,
          status: 'paid',
          data: { subscription: data.subscription, payment: data.payment }
        };

      case 'PAYMENT_FAILED':
        return {
          type: 'payment.failed',
          subscriptionId: data.subscription.subscriptionId,
          status: 'payment_failed',
          data: { subscription: data.subscription, payment: data.payment }
        };

      default:
        return {
          type: 'unknown',
          subscriptionId: data.subscription?.subscriptionId || '',
          status: 'unknown',
          data: event
        };
    }
  }

  // Create payment link for one-time payments
  async createPaymentLink(paymentData: {
    orderId: string;
    amount: number;
    currency: string;
    customerName: string;
    customerEmail: string;
    customerPhone: string;
    returnUrl: string;
    notifyUrl: string;
    description: string;
  }) {
    try {
      const response = await fetch(`${this.baseUrl}/pg/orders`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          order_id: paymentData.orderId,
          order_amount: paymentData.amount,
          order_currency: paymentData.currency,
          customer_details: {
            customer_id: `customer_${Date.now()}`,
            customer_name: paymentData.customerName,
            customer_email: paymentData.customerEmail,
            customer_phone: paymentData.customerPhone
          },
          order_meta: {
            return_url: paymentData.returnUrl,
            notify_url: paymentData.notifyUrl
          },
          order_note: paymentData.description
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`Cashfree API Error: ${error.message || 'Unknown error'}`);
      }

      const orderData = await response.json();

      // Create payment session
      const sessionResponse = await fetch(`${this.baseUrl}/pg/orders/${paymentData.orderId}/pay`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          payment_session_id: orderData.payment_session_id
        })
      });

      if (!sessionResponse.ok) {
        const error = await sessionResponse.json();
        throw new Error(`Cashfree API Error: ${error.message || 'Unknown error'}`);
      }

      return await sessionResponse.json();
    } catch (error) {
      console.error('Error creating payment link:', error);
      throw error;
    }
  }

  // Get payment status
  async getPaymentStatus(orderId: string) {
    try {
      const response = await fetch(`${this.baseUrl}/pg/orders/${orderId}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`Cashfree API Error: ${error.message || 'Unknown error'}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching payment status:', error);
      throw error;
    }
  }
}

// Initialize Cashfree service
export const cashfreeService = new CashfreeService({
  appId: process.env.CASHFREE_APP_ID || '',
  secretKey: process.env.CASHFREE_SECRET_KEY || '',
  environment: (process.env.CASHFREE_ENVIRONMENT as 'sandbox' | 'production') || 'sandbox',
  webhookSecret: process.env.CASHFREE_WEBHOOK_SECRET || ''
});

// Utility functions
export const generateSubscriptionId = (userId: string): string => {
  return `sub_${userId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

export const generateCustomerId = (userId: string): string => {
  return `customer_${userId}`;
};

export const generateOrderId = (): string => {
  return `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};
