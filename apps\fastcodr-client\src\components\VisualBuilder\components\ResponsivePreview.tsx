import React, { useState, useRef, useEffect } from 'react';
import { Monitor, Tablet, Smartphone, RotateCcw, Maximize2, Minimize2 } from 'lucide-react';
import { UIComponent } from '../VisualBuilder';
import RenderableComponent from './RenderableComponent';

interface ResponsivePreviewProps {
  components: UIComponent[];
  selectedId: string | null;
  onSelect: (id: string) => void;
  onUpdate: (id: string, updates: Partial<UIComponent>) => void;
  className?: string;
}

type ViewportSize = 'mobile' | 'tablet' | 'desktop';
type Orientation = 'portrait' | 'landscape';

const VIEWPORT_SIZES = {
  mobile: { width: 375, height: 667, name: 'Mobile' },
  tablet: { width: 768, height: 1024, name: 'Tablet' },
  desktop: { width: 1200, height: 800, name: 'Desktop' }
};

const ResponsivePreview: React.FC<ResponsivePreviewProps> = ({
  components,
  selectedId,
  onSelect,
  onUpdate,
  className = ''
}) => {
  const [viewport, setViewport] = useState<ViewportSize>('desktop');
  const [orientation, setOrientation] = useState<Orientation>('portrait');
  const [zoom, setZoom] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const previewRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Calculate responsive dimensions
  const getViewportDimensions = () => {
    const size = VIEWPORT_SIZES[viewport];
    if (viewport === 'desktop') {
      return { width: size.width, height: size.height };
    }
    
    if (orientation === 'landscape') {
      return { width: size.height, height: size.width };
    }
    
    return { width: size.width, height: size.height };
  };

  const { width, height } = getViewportDimensions();

  // Auto-fit zoom calculation
  useEffect(() => {
    if (!containerRef.current || viewport === 'desktop') {
      setZoom(1);
      return;
    }

    const container = containerRef.current;
    const containerWidth = container.clientWidth - 40; // padding
    const containerHeight = container.clientHeight - 40;
    
    const scaleX = containerWidth / width;
    const scaleY = containerHeight / height;
    const scale = Math.min(scaleX, scaleY, 1);
    
    setZoom(scale);
  }, [width, height, viewport]);

  const handleViewportChange = (newViewport: ViewportSize) => {
    setViewport(newViewport);
    if (newViewport === 'desktop') {
      setOrientation('landscape');
    } else {
      setOrientation('portrait');
    }
  };

  const toggleOrientation = () => {
    if (viewport !== 'desktop') {
      setOrientation(prev => prev === 'portrait' ? 'landscape' : 'portrait');
    }
  };

  const handleZoomChange = (newZoom: number) => {
    setZoom(Math.max(0.25, Math.min(2, newZoom)));
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const getDeviceFrame = () => {
    if (viewport === 'desktop') return '';
    
    return `
      relative bg-gray-900 rounded-[2rem] p-2
      ${viewport === 'mobile' ? 'shadow-xl' : 'shadow-2xl'}
      before:absolute before:top-0 before:left-1/2 before:transform before:-translate-x-1/2
      before:w-16 before:h-1 before:bg-gray-700 before:rounded-full before:mt-2
      after:absolute after:bottom-2 after:left-1/2 after:transform after:-translate-x-1/2
      after:w-8 after:h-8 after:bg-gray-800 after:rounded-full
    `;
  };

  return (
    <div className={`flex flex-col h-full bg-gray-50 ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
        <div className="flex items-center gap-4">
          <h3 className="font-semibold text-gray-900">Responsive Preview</h3>
          
          {/* Viewport Controls */}
          <div className="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
            {Object.entries(VIEWPORT_SIZES).map(([key, size]) => {
              const isActive = viewport === key;
              const Icon = key === 'desktop' ? Monitor : key === 'tablet' ? Tablet : Smartphone;
              
              return (
                <button
                  key={key}
                  onClick={() => handleViewportChange(key as ViewportSize)}
                  className={`
                    flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all
                    ${isActive 
                      ? 'bg-white shadow text-blue-600' 
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
                    }
                  `}
                  title={`${size.name} (${size.width}x${size.height})`}
                >
                  <Icon className="w-4 h-4" />
                  {size.name}
                </button>
              );
            })}
          </div>

          {/* Orientation Toggle */}
          {viewport !== 'desktop' && (
            <button
              onClick={toggleOrientation}
              className="flex items-center gap-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              title="Toggle orientation"
            >
              <RotateCcw className="w-4 h-4" />
              {orientation === 'portrait' ? 'Portrait' : 'Landscape'}
            </button>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* Zoom Controls */}
          <div className="flex items-center gap-2">
            <button
              onClick={() => handleZoomChange(zoom - 0.25)}
              className="px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-sm"
              disabled={zoom <= 0.25}
            >
              -
            </button>
            <span className="text-sm font-mono min-w-[4rem] text-center">
              {Math.round(zoom * 100)}%
            </span>
            <button
              onClick={() => handleZoomChange(zoom + 0.25)}
              className="px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-sm"
              disabled={zoom >= 2}
            >
              +
            </button>
          </div>

          {/* Fullscreen Toggle */}
          <button
            onClick={toggleFullscreen}
            className="p-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            title="Toggle fullscreen"
          >
            {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
          </button>
        </div>
      </div>

      {/* Preview Area */}
      <div 
        ref={containerRef}
        className={`
          flex-1 flex items-center justify-center p-4 overflow-auto
          ${isFullscreen ? 'fixed inset-0 z-50 bg-gray-50' : ''}
        `}
      >
        <div
          className={`
            transition-all duration-300 ease-in-out
            ${viewport !== 'desktop' ? getDeviceFrame() : ''}
          `}
          style={{
            transform: `scale(${zoom})`,
            transformOrigin: 'center center'
          }}
        >
          <div
            ref={previewRef}
            className="bg-white overflow-auto"
            style={{
              width: `${width}px`,
              height: `${height}px`,
              borderRadius: viewport !== 'desktop' ? '1rem' : '0.5rem',
              border: viewport === 'desktop' ? '1px solid #e5e7eb' : 'none'
            }}
          >
            {/* Viewport Info Bar */}
            <div className="bg-gray-100 px-4 py-2 text-xs text-gray-600 border-b border-gray-200 flex items-center justify-between">
              <span>
                {VIEWPORT_SIZES[viewport].name} • {width} × {height}
              </span>
              <span className="text-gray-400">
                {orientation === 'portrait' ? '📱' : '📱↻'}
              </span>
            </div>

            {/* Component Rendering Area */}
            <div className="p-4 min-h-[calc(100%-2.5rem)]">
              {components.length === 0 ? (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <div className="text-4xl mb-4">📱</div>
                    <p>No components to preview</p>
                    <p className="text-sm text-gray-400 mt-1">
                      Add components to see them here
                    </p>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {components.map((component) => (
                    <RenderableComponent
                      key={component.id}
                      component={component}
                      isSelected={selectedId === component.id}
                      onSelect={onSelect}
                      onDrop={() => {}} // Preview mode - no dropping
                      onUpdate={onUpdate}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Device Info */}
      <div className="bg-white border-t border-gray-200 px-4 py-2">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center gap-4">
            <span>
              Viewport: <strong>{width} × {height}</strong>
            </span>
            <span>
              Zoom: <strong>{Math.round(zoom * 100)}%</strong>
            </span>
            <span>
              Orientation: <strong>{orientation}</strong>
            </span>
          </div>
          
          <div className="flex items-center gap-2 text-xs">
            <div className="w-3 h-3 bg-green-500 rounded-full" title="Live preview"></div>
            Live Preview
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResponsivePreview;
