import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, UserRole, Permission, canPerformAction } from '@/lib/auth';
import { createOpenAI } from '@ai-sdk/openai';
import { streamText, generateText } from 'ai';

interface AIAssistantRequest {
  prompt: string;
  code?: string;
  template?: string;
  model?: string;
  stream?: boolean;
  context?: {
    language?: string;
    framework?: string;
    projectType?: string;
  };
}

// AI prompt templates for different use cases
const AI_TEMPLATES = {
  'explain-code': 'Please explain what this code does, how it works, and any important details:',
  'debug-code': 'Please analyze this code for potential bugs, errors, or issues and suggest fixes:',
  'optimize-code': 'Please optimize this code for better performance and efficiency:',
  'refactor-code': 'Please refactor this code to make it cleaner, more maintainable, and follow best practices:',
  'generate-tests': 'Please generate comprehensive unit tests for this code:',
  'add-comments': 'Please add clear and helpful comments to this code:',
  'security-review': 'Please review this code for security vulnerabilities and suggest improvements:',
  'convert-language': 'Please convert this code to the specified programming language:',
  'generate-docs': 'Please generate comprehensive documentation for this code:'
};

export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    const authResult = await requireAuth(UserRole.USER, [Permission.AI_ASSISTANT])(request);
    
    if (authResult instanceof Response) {
      return authResult;
    }

    const { user } = authResult;

    // Parse request body
    const {
      prompt,
      code,
      template,
      model = 'claude-3-5-sonnet',
      stream = true,
      context
    }: AIAssistantRequest = await request.json();

    if (!prompt && !template) {
      return NextResponse.json(
        { error: 'Prompt or template is required' },
        { status: 400 }
      );
    }

    // Check usage limits
    const mockCurrentUsage = {
      projectsThisMonth: 0,
      aiCallsThisMonth: 45, // This should come from database
      storageUsed: 0,
      resetDate: new Date()
    };

    if (!canPerformAction(user.role, mockCurrentUsage, 'aiCall')) {
      return NextResponse.json({
        error: 'AI call limit exceeded for your plan',
        upgradeRequired: user.role === UserRole.USER,
        remainingCalls: 0
      }, { status: 429 });
    }

    // Build the full prompt
    let fullPrompt = '';
    
    if (template && AI_TEMPLATES[template as keyof typeof AI_TEMPLATES]) {
      fullPrompt = AI_TEMPLATES[template as keyof typeof AI_TEMPLATES];
    } else {
      fullPrompt = prompt;
    }

    // Add context if provided
    if (context) {
      const contextInfo = [];
      if (context.language) contextInfo.push(`Language: ${context.language}`);
      if (context.framework) contextInfo.push(`Framework: ${context.framework}`);
      if (context.projectType) contextInfo.push(`Project Type: ${context.projectType}`);
      
      if (contextInfo.length > 0) {
        fullPrompt += `\n\nContext: ${contextInfo.join(', ')}`;
      }
    }

    // Add code if provided
    if (code) {
      fullPrompt += `\n\nCode:\n\`\`\`\n${code}\n\`\`\``;
    }

    // Initialize OpenAI
    const openai = createOpenAI({
      baseURL: process.env.THIRD_API_URL,
      apiKey: process.env.THIRD_API_KEY,
    });

    // System prompt for code assistance
    const systemPrompt = `You are FastCodr AI Assistant, an expert programming assistant. You help developers with:
- Code explanation and documentation
- Bug detection and debugging
- Code optimization and refactoring
- Security analysis
- Test generation
- Best practices and recommendations

Always provide:
1. Clear, concise explanations
2. Working code examples when applicable
3. Best practices and security considerations
4. Performance implications
5. Alternative approaches when relevant

Format code blocks with proper syntax highlighting using triple backticks and language specification.`;

    if (stream) {
      // Streaming response
      const result = await streamText({
        model: openai(model),
        system: systemPrompt,
        prompt: fullPrompt,
        temperature: 0.7,
        maxTokens: 2000,
      });

      // TODO: Increment AI call usage in database
      // await incrementAICallUsage(user.userId);

      return result.toAIStreamResponse();
    } else {
      // Non-streaming response
      const result = await generateText({
        model: openai(model),
        system: systemPrompt,
        prompt: fullPrompt,
        temperature: 0.7,
        maxTokens: 2000,
      });

      // TODO: Increment AI call usage in database
      // await incrementAICallUsage(user.userId);

      return NextResponse.json({
        success: true,
        response: result.text,
        usage: {
          promptTokens: result.usage?.promptTokens || 0,
          completionTokens: result.usage?.completionTokens || 0,
          totalTokens: result.usage?.totalTokens || 0
        },
        model: model
      });
    }

  } catch (error) {
    console.error('AI Assistant error:', error);

    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { error: 'Invalid or missing API key' },
          { status: 401 }
        );
      }
      
      if (error.message.includes('rate limit')) {
        return NextResponse.json(
          { error: 'Rate limit exceeded. Please try again later.' },
          { status: 429 }
        );
      }
    }

    return NextResponse.json(
      { error: 'AI Assistant service temporarily unavailable' },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve available templates and user limits
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(UserRole.USER, [Permission.AI_ASSISTANT])(request);
    
    if (authResult instanceof Response) {
      return authResult;
    }

    const { user } = authResult;

    // Get current usage (mock data - replace with database query)
    const mockCurrentUsage = {
      projectsThisMonth: 0,
      aiCallsThisMonth: 45,
      storageUsed: 0,
      resetDate: new Date()
    };

    // Calculate remaining calls
    const userLimits = {
      [UserRole.USER]: { aiCallsPerMonth: 50 },
      [UserRole.SUBSCRIBER]: { aiCallsPerMonth: 1000 },
      [UserRole.ADMIN]: { aiCallsPerMonth: -1 }
    };

    const limit = userLimits[user.role].aiCallsPerMonth;
    const remainingCalls = limit === -1 ? -1 : Math.max(0, limit - mockCurrentUsage.aiCallsThisMonth);

    return NextResponse.json({
      success: true,
      data: {
        templates: Object.keys(AI_TEMPLATES).map(key => ({
          id: key,
          name: key.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()),
          prompt: AI_TEMPLATES[key as keyof typeof AI_TEMPLATES]
        })),
        usage: {
          remainingCalls,
          totalCalls: limit,
          usedCalls: mockCurrentUsage.aiCallsThisMonth,
          resetDate: mockCurrentUsage.resetDate
        },
        models: [
          'claude-3-5-sonnet',
          'gpt-4o-mini',
          'deepseek-chat'
        ]
      }
    });

  } catch (error) {
    console.error('AI Assistant GET error:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve AI Assistant information' },
      { status: 500 }
    );
  }
}
