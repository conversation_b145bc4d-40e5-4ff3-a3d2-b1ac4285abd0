import React from 'react';
import { useDrag } from 'react-dnd';
import { LucideIcon } from 'lucide-react';

interface ComponentPaletteItemProps {
  type: string;
  config: {
    name: string;
    icon: LucideIcon;
    defaultProps: Record<string, any>;
  };
  onAdd: () => void;
}

const ComponentPaletteItem: React.FC<ComponentPaletteItemProps> = ({ type, config, onAdd }) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'component',
    item: { type },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  const Icon = config.icon;

  return (
    <div
      ref={drag}
      onClick={onAdd}
      className={`
        flex flex-col items-center gap-1 p-3 border border-gray-200 rounded-lg cursor-pointer
        hover:border-blue-300 hover:bg-blue-50 transition-colors
        ${isDragging ? 'opacity-50' : ''}
      `}
      title={`Add ${config.name}`}
    >
      <Icon className="w-5 h-5 text-gray-600" />
      <span className="text-xs text-gray-700 text-center leading-tight">{config.name}</span>
    </div>
  );
};

export default ComponentPaletteItem;
