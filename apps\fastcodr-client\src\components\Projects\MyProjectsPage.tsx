import React, { useState, useEffect } from 'react';
import {
  Plus,
  Search,
  Filter,
  Grid,
  List,
  Calendar,
  Star,
  Eye,
  GitFork,
  MoreVertical,
  Edit,
  Trash2,
  Share,
  Download,
  Clock,
  FileText
} from 'lucide-react';
import useProjectStore, { Project } from '@/stores/projectSlice';
import useUserStore from '@/stores/userSlice';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { Permission, UserRole } from '@/types/auth';
import { toast } from 'react-hot-toast';
import { formatDistanceToNow } from 'date-fns';
import ProjectCard from './ProjectCard';
import CreateProjectModal from './CreateProjectModal';

type ViewMode = 'grid' | 'list';
type SortBy = 'updatedAt' | 'createdAt' | 'name' | 'viewCount';
type FilterBy = 'all' | 'draft' | 'published' | 'public' | 'private';

const MyProjectsPage: React.FC = () => {
  const {
    projects,
    isLoading,
    loadProjects,
    deleteProject,
    setCurrentProject
  } = useProjectStore();

  const { getRemainingUsage } = useUserStore();
  const remainingUsage = getRemainingUsage();

  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortBy, setSortBy] = useState<SortBy>('updatedAt');
  const [filterBy, setFilterBy] = useState<FilterBy>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    loadProjects();
  }, [loadProjects]);

  // Filter and sort projects
  const filteredProjects = projects
    .filter(project => {
      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        if (!project.name.toLowerCase().includes(query) &&
            !project.description.toLowerCase().includes(query) &&
            !project.tags.some(tag => tag.toLowerCase().includes(query))) {
          return false;
        }
      }

      // Status filter
      switch (filterBy) {
        case 'draft':
          return project.status === 'draft';
        case 'published':
          return project.status === 'published';
        case 'public':
          return project.isPublic;
        case 'private':
          return !project.isPublic;
        default:
          return true;
      }
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'createdAt':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'viewCount':
          return b.viewCount - a.viewCount;
        case 'updatedAt':
        default:
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
      }
    });

  const handleProjectClick = (project: Project) => {
    setCurrentProject(project);
    // Navigate to editor
    window.location.href = `/editor?project=${project.id}`;
  };

  const handleDeleteProject = async (projectId: string) => {
    if (window.confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
      await deleteProject(projectId);
    }
  };

  const getProjectStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      case 'archived':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getFrameworkIcon = (framework: string) => {
    // You can replace these with actual framework icons
    const icons: Record<string, string> = {
      react: '⚛️',
      vue: '💚',
      angular: '🅰️',
      svelte: '🧡',
      nextjs: '▲',
      nuxt: '💚',
      express: '🚀',
      nodejs: '💚',
      python: '🐍',
      java: '☕',
      flutter: '💙'
    };
    return icons[framework] || '📁';
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your projects...</p>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute requiredPermissions={[Permission.SAVE_PROJECT]}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  My Projects
                </h1>
                <p className="mt-2 text-gray-600 dark:text-gray-400">
                  Manage and organize your FastCodr projects
                </p>
              </div>

              <div className="flex items-center gap-4">
                {/* Usage indicator */}
                {remainingUsage.projects !== -1 && (
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    <span className="font-medium">{remainingUsage.projects}</span> projects remaining
                  </div>
                )}

                <button
                  onClick={() => setShowCreateModal(true)}
                  disabled={remainingUsage.projects === 0}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                >
                  <Plus className="w-4 h-4" />
                  New Project
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Controls */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            {/* Search and Filters */}
            <div className="flex flex-1 gap-4 items-center">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search projects..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <select
                value={filterBy}
                onChange={(e) => setFilterBy(e.target.value as FilterBy)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="all">All Projects</option>
                <option value="draft">Drafts</option>
                <option value="published">Published</option>
                <option value="public">Public</option>
                <option value="private">Private</option>
              </select>

              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as SortBy)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="updatedAt">Last Updated</option>
                <option value="createdAt">Date Created</option>
                <option value="name">Name</option>
                <option value="viewCount">Most Viewed</option>
              </select>
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center gap-2 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded ${viewMode === 'grid' ? 'bg-white dark:bg-gray-600 shadow' : 'hover:bg-gray-200 dark:hover:bg-gray-600'}`}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded ${viewMode === 'list' ? 'bg-white dark:bg-gray-600 shadow' : 'hover:bg-gray-200 dark:hover:bg-gray-600'}`}
              >
                <List className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Projects Grid/List */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
          {filteredProjects.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {searchQuery || filterBy !== 'all' ? 'No projects found' : 'No projects yet'}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {searchQuery || filterBy !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'Create your first project to get started'
                }
              </p>
              {(!searchQuery && filterBy === 'all') && (
                <button
                  onClick={() => setShowCreateModal(true)}
                  className="px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
                >
                  Create Your First Project
                </button>
              )}
            </div>
          ) : (
            <div className={
              viewMode === 'grid'
                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                : 'space-y-4'
            }>
              {filteredProjects.map((project) => (
                <ProjectCard
                  key={project.id}
                  project={project}
                  viewMode={viewMode}
                  onOpen={() => handleProjectClick(project)}
                  onDelete={() => handleDeleteProject(project.id)}
                  getFrameworkIcon={getFrameworkIcon}
                  getProjectStatusColor={getProjectStatusColor}
                />
              ))}
            </div>
          )}
        </div>

        {/* Create Project Modal */}
        {showCreateModal && (
          <CreateProjectModal
            onClose={() => setShowCreateModal(false)}
            onSuccess={() => {
              setShowCreateModal(false);
              loadProjects();
            }}
          />
        )}
      </div>
    </ProtectedRoute>
  );
};

export default MyProjectsPage;
