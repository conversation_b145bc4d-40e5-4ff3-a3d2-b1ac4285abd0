# 🚀 FastCodr Deployment Guide

This guide covers deploying FastCodr in various environments, from development to production.

## 📋 Prerequisites

### Required Software
- **Docker** (v20.10+)
- **Docker Compose** (v2.0+)
- **Node.js** (v18+) - for development
- **Git** - for version control

### Required Services
- **MongoDB** - Database
- **Redis** - Caching and sessions (optional)
- **Cashfree Account** - Payment processing
- **OpenAI API Key** - AI functionality

## 🔧 Environment Configuration

### 1. Backend Configuration
Copy and configure the backend environment file:
```bash
cp apps/fastcodr-next/.env.example apps/fastcodr-next/.env.production
```

Update the following variables:
```env
# AI Model API Configuration
THIRD_API_URL=https://api.openai.com/v1
THIRD_API_KEY=your_openai_api_key

# MongoDB Connection
MONGODB_URI=**************************************************

# JWT Secret
JWT_SECRET=your_secure_jwt_secret

# CORS Configuration
ALLOWED_ORIGINS=https://yourdomain.com
```

### 2. Frontend Configuration
Copy and configure the frontend environment file:
```bash
cp apps/fastcodr-client/.env.example apps/fastcodr-client/.env.production
```

Update the following variables:
```env
# Backend API URL
APP_BASE_URL=https://api.yourdomain.com

# Cashfree Configuration
VITE_CASHFREE_CLIENT_ID=your_cashfree_client_id
VITE_CASHFREE_CLIENT_SECRET=your_cashfree_client_secret
VITE_CASHFREE_BASE_URL=https://api.cashfree.com/pg
VITE_CASHFREE_ENV=production
```

## 🐳 Docker Deployment

### Quick Start
Run the deployment script:

**Linux/macOS:**
```bash
chmod +x deploy.sh
./deploy.sh
```

**Windows:**
```powershell
.\deploy.ps1
```

### Manual Deployment
1. **Build images:**
   ```bash
   docker-compose build
   ```

2. **Start services:**
   ```bash
   docker-compose up -d
   ```

3. **Check status:**
   ```bash
   docker-compose ps
   ```

4. **View logs:**
   ```bash
   docker-compose logs -f
   ```

## 🌐 Production Deployment

### 1. Cloud Platforms

#### AWS Deployment
1. **ECS with Fargate:**
   - Use the provided Dockerfiles
   - Configure Application Load Balancer
   - Set up RDS for MongoDB or use DocumentDB

2. **EC2 Deployment:**
   - Launch EC2 instances
   - Install Docker and Docker Compose
   - Run deployment scripts

#### Google Cloud Platform
1. **Cloud Run:**
   - Deploy containers to Cloud Run
   - Use Cloud SQL for MongoDB
   - Configure Cloud Load Balancer

#### Azure Deployment
1. **Container Instances:**
   - Deploy using Azure Container Instances
   - Use Azure Database for MongoDB
   - Configure Application Gateway

### 2. VPS Deployment
1. **Prepare server:**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sh get-docker.sh
   
   # Install Docker Compose
   sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   ```

2. **Deploy application:**
   ```bash
   git clone https://github.com/yourusername/fastcodr.git
   cd fastcodr
   ./deploy.sh
   ```

## 🔒 SSL/HTTPS Configuration

### 1. Using Let's Encrypt
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 2. Using Cloudflare
1. Point your domain to Cloudflare
2. Enable SSL/TLS encryption
3. Configure origin certificates

## 📊 Monitoring and Logging

### 1. Application Monitoring
- **Sentry** - Error tracking
- **New Relic** - Performance monitoring
- **DataDog** - Infrastructure monitoring

### 2. Log Management
```bash
# View application logs
docker-compose logs -f backend
docker-compose logs -f frontend

# Log rotation
sudo logrotate -f /etc/logrotate.conf
```

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy FastCodr
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to production
        run: |
          ssh user@server 'cd /path/to/fastcodr && git pull && ./deploy.sh'
```

## 🛠️ Maintenance

### 1. Updates
```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose down
docker-compose build
docker-compose up -d
```

### 2. Backup
```bash
# Backup MongoDB
docker-compose exec mongodb mongodump --out /backup

# Backup application data
tar -czf fastcodr-backup-$(date +%Y%m%d).tar.gz apps/
```

### 3. Scaling
```bash
# Scale backend instances
docker-compose up -d --scale backend=3

# Use load balancer for multiple instances
```

## 🚨 Troubleshooting

### Common Issues

1. **Port conflicts:**
   ```bash
   # Check port usage
   netstat -tulpn | grep :3001
   
   # Change ports in docker-compose.yml
   ```

2. **Memory issues:**
   ```bash
   # Increase Docker memory limit
   # Check system resources
   docker stats
   ```

3. **Database connection:**
   ```bash
   # Check MongoDB logs
   docker-compose logs mongodb
   
   # Test connection
   docker-compose exec backend npm run test:db
   ```

## 📞 Support

For deployment issues:
1. Check the logs: `docker-compose logs`
2. Verify environment variables
3. Ensure all required services are running
4. Check firewall and network settings

## 🎯 Performance Optimization

### 1. Frontend Optimization
- Enable gzip compression
- Use CDN for static assets
- Implement service workers
- Optimize bundle size

### 2. Backend Optimization
- Enable Redis caching
- Optimize database queries
- Use connection pooling
- Implement rate limiting

### 3. Database Optimization
- Create proper indexes
- Use MongoDB sharding for scale
- Regular maintenance and cleanup
- Monitor query performance
