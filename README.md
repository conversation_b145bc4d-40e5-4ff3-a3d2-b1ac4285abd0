# FastCodr - Code Smarter, Faster ⚡

> **FastCodr is a lightning-fast coding companion that boosts developer productivity.**

![FastCodr Banner](./docs/img/image-1.png)

## 🚀 What is FastCodr?

<PERSON>Codr is your intelligent, fast, and focused development assistant that revolutionizes how you build applications. Powered by cutting-edge AI technology, FastCodr transforms ideas into production-ready code in seconds.

## ✨ What Makes FastCodr Different?

FastCodr stands out from other AI coding tools like Cursor, v0, and Bolt.new with these powerful features:

🔧 **Browser-based Development Environment**: Built-in WebContainer allows you to run terminals, install packages, and debug directly in your browser.

🎨 **High-Fidelity Design-to-Code**: Advanced D2C technology achieves 90% design restoration accuracy from Sketch, Figma, and image files.

📁 **Existing Project Support**: Unlike browser-only tools, FastCodr can import and work with your existing codebases for seamless development.

📱 **WeChat Mini Program Integration**: Direct integration with WeChat Developer Tools for instant preview and debugging.

🌐 **Multi-Platform Support**: Available on Windows, Mac, and web - choose the environment that works best for you.

🤖 **Advanced AI Models**: Support for multiple AI providers including OpenAI, Deepseek, Claude, and more.

🔌 **MCP Protocol Support**: Extensible through Model Context Protocol for enhanced functionality.

| Feature                                    | FastCodr | v0  | bolt.new |
| ------------------------------------------ | -------- | --- | -------- |
| Code generation and preview                | ✅       | ✅  | ✅       |
| Design-to-code conversion                  | ✅       | ✅  | ✅       |
| Open-source                                | ✅       | ❌  | ✅       |
| Supports WeChat Mini Program Tools preview | ✅       | ❌  | ❌       |
| Supports existing projects                 | ✅       | ❌  | ❌       |
| Supports Deepseek                          | ✅       | ❌  | ❌       |
| Supports MCP                               | ✅       | ❌  | ❌       |
| Multi-platform desktop app                | ✅       | ❌  | ❌       |

## 🛠️ Quick Start

### Prerequisites
- Node.js 18.20 or higher
- pnpm package manager

### 1. Install pnpm
```bash
npm install pnpm -g
```

### 2. Install Dependencies
```bash
# Install client dependencies
cd apps/fastcodr-client
pnpm install

# Install server dependencies
cd ../fastcodr-next
pnpm install
```

### 3. Configure Environment Variables

**Client Configuration** (`apps/fastcodr-client/.env`):
```shell
# Server address [REQUIRED] (e.g., http://localhost:3000)
APP_BASE_URL=

# JWT secret [OPTIONAL]
JWT_SECRET=
```

**Server Configuration** (`apps/fastcodr-next/.env`):
```shell
# AI Model API URL [REQUIRED] (e.g., https://api.openai.com/v1)
THIRD_API_URL=

# AI Model API Key [REQUIRED] (e.g., sk-xxxx)
THIRD_API_KEY=

# JWT secret [OPTIONAL]
JWT_SECRET=
```

### 4. Build and Run

**Build the Web Editor:**
```bash
chmod +x scripts/fastcodr-build.sh
./scripts/fastcodr-build.sh
```

**Development Mode:**
```bash
# Start the server
pnpm dev:next

# Start the client (in another terminal)
pnpm dev:client
```

## 📦 Installation Options

### Desktop Application
Download the latest release for your platform:
- **Windows**: Download the `.exe` installer
- **macOS**: Download the `.dmg` file

### Web Version
Access FastCodr directly in your browser at the hosted URL.

## 🔧 Troubleshooting

**Electron Issues:**
- If Electron reports errors on second run, delete the client workspace
- If preview doesn't show when starting, run `pnpm run electron:dev`

**Build Issues:**
- Ensure Node.js version is 18.20 or higher
- Clear node_modules and reinstall dependencies if needed

## 🤝 Contributing

We welcome contributions! Please feel free to submit issues and pull requests.

## 📧 Contact

For questions and support, please contact:
- **Email**: <EMAIL>
- **Author**: Jitender Kumar

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**FastCodr - Code Smarter, Faster** ⚡
