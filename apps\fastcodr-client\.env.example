# FastCodr Environment Configuration

# Cashfree Payment Gateway Configuration
# Get these from your Cashfree dashboard: https://merchant.cashfree.com/
VITE_CASHFREE_CLIENT_ID=your_cashfree_client_id_here
VITE_CASHFREE_CLIENT_SECRET=your_cashfree_client_secret_here
VITE_CASHFREE_BASE_URL=https://sandbox.cashfree.com/pg
VITE_CASHFREE_PLAN_ID=fastcodr_premium_monthly

# Environment (sandbox or production)
VITE_CASHFREE_ENV=sandbox

# Server Configuration
APP_BASE_URL=http://localhost:3001

# JWT Secret (optional)
JWT_SECRET=your_jwt_secret_here

# Instructions:
# 1. Copy this file to .env
# 2. Replace the placeholder values with your actual Cashfree credentials
# 3. For production, change VITE_CASHFREE_BASE_URL to https://api.cashfree.com/pg
# 4. For production, change VITE_CASHFREE_ENV to production
