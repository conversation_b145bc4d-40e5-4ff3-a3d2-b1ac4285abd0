import React from 'react';
import { useDrop } from 'react-dnd';
import { UIComponent } from '../VisualBuilder';

interface RenderableComponentProps {
  component: UIComponent;
  isSelected: boolean;
  onSelect: (id: string) => void;
  onDrop: (type: string, parentId?: string) => void;
  onUpdate: (id: string, updates: Partial<UIComponent>) => void;
}

const RenderableComponent: React.FC<RenderableComponentProps> = ({
  component,
  isSelected,
  onSelect,
  onDrop,
  onUpdate
}) => {
  const [{ isOver }, drop] = useDrop(() => ({
    accept: 'component',
    drop: (item: { type: string }, monitor) => {
      if (monitor.didDrop()) return; // Prevent duplicate drops
      onDrop(item.type, component.id);
    },
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
    }),
  }));

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect(component.id);
  };

  const handleContentEdit = (e: React.FocusEvent<HTMLElement>) => {
    const newContent = e.target.textContent || '';
    if (newContent !== component.props.children) {
      onUpdate(component.id, {
        props: { ...component.props, children: newContent }
      });
    }
  };

  // Create the React element
  const createReactElement = () => {
    const { type, props, children } = component;
    
    // Handle text content for editable elements
    const isTextElement = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span', 'button'].includes(type);
    const hasTextContent = props.children && typeof props.children === 'string';
    
    const elementProps: any = {
      ...props,
      onClick: handleClick,
      className: `${props.className || ''} ${isSelected ? 'ring-2 ring-blue-500 ring-opacity-50' : ''} ${isOver ? 'ring-2 ring-green-500 ring-opacity-50' : ''}`.trim(),
      style: {
        ...component.style,
        position: 'relative',
        minHeight: type === 'div' ? '40px' : undefined,
      }
    };

    // Add contentEditable for text elements
    if (isTextElement && hasTextContent) {
      elementProps.contentEditable = true;
      elementProps.suppressContentEditableWarning = true;
      elementProps.onBlur = handleContentEdit;
      elementProps.style = {
        ...elementProps.style,
        outline: 'none',
      };
    }

    // Handle different element types
    switch (type) {
      case 'img':
        return React.createElement('img', {
          ...elementProps,
          contentEditable: false,
          draggable: false,
        });
      
      case 'input':
        return React.createElement('input', {
          ...elementProps,
          contentEditable: false,
        });
      
      case 'button':
        return React.createElement('button', elementProps, hasTextContent ? props.children : 'Button');
      
      default:
        // For container elements, render children
        const childElements = children && children.length > 0 
          ? children.map((child) => (
              <RenderableComponent
                key={child.id}
                component={child}
                isSelected={isSelected}
                onSelect={onSelect}
                onDrop={onDrop}
                onUpdate={onUpdate}
              />
            ))
          : hasTextContent 
            ? props.children 
            : null;

        return React.createElement(
          type,
          elementProps,
          childElements
        );
    }
  };

  return (
    <div
      ref={drop}
      className={`
        relative group
        ${isSelected ? 'z-10' : ''}
        ${isOver ? 'bg-green-50' : ''}
      `}
    >
      {createReactElement()}
      
      {/* Selection indicator */}
      {isSelected && (
        <div className="absolute -top-6 left-0 bg-blue-500 text-white text-xs px-2 py-1 rounded pointer-events-none z-20">
          {component.type}
        </div>
      )}
      
      {/* Drop indicator */}
      {isOver && (
        <div className="absolute inset-0 border-2 border-dashed border-green-400 bg-green-50 bg-opacity-50 pointer-events-none z-10 flex items-center justify-center">
          <span className="bg-green-500 text-white text-xs px-2 py-1 rounded">
            Drop here
          </span>
        </div>
      )}
    </div>
  );
};

export default RenderableComponent;
