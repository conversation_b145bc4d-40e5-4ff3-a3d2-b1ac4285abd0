import React from 'react';
import useUserStore from '@/stores/userSlice';
import { UserRole, Permission } from '@/types/auth';
import { Lock, Crown, AlertTriangle } from 'lucide-react';

interface PermissionGuardProps {
  children: React.ReactNode;
  requiredRole?: UserRole;
  requiredPermissions?: Permission[];
  fallback?: React.ReactNode;
  showUpgradePrompt?: boolean;
  inline?: boolean;
}

const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  requiredRole,
  requiredPermissions = [],
  fallback,
  showUpgradePrompt = true,
  inline = false
}) => {
  const { user, hasPermission, isAuthenticated } = useUserStore();

  // If not authenticated, don't show anything
  if (!isAuthenticated || !user) {
    return fallback ? <>{fallback}</> : null;
  }

  // Check role-based access
  if (requiredRole) {
    const roleHierarchy = [UserRole.USER, UserRole.SUBSCRIBER, UserRole.ADMIN];
    const userRoleIndex = roleHierarchy.indexOf(user.role);
    const requiredRoleIndex = roleHierarchy.indexOf(requiredRole);
    
    if (userRoleIndex < requiredRoleIndex) {
      if (showUpgradePrompt) {
        return inline ? 
          <InlineUpgradePrompt requiredRole={requiredRole} /> : 
          <BlockUpgradePrompt requiredRole={requiredRole} />;
      }
      return fallback ? <>{fallback}</> : null;
    }
  }

  // Check specific permissions
  if (requiredPermissions.length > 0) {
    const hasAllPermissions = requiredPermissions.every(permission => 
      hasPermission(permission)
    );
    
    if (!hasAllPermissions) {
      if (showUpgradePrompt) {
        return inline ? 
          <InlineUpgradePrompt requiredPermissions={requiredPermissions} /> : 
          <BlockUpgradePrompt requiredPermissions={requiredPermissions} />;
      }
      return fallback ? <>{fallback}</> : null;
    }
  }

  return <>{children}</>;
};

interface UpgradePromptProps {
  requiredRole?: UserRole;
  requiredPermissions?: Permission[];
}

const InlineUpgradePrompt: React.FC<UpgradePromptProps> = ({ requiredRole, requiredPermissions }) => {
  const getPromptInfo = () => {
    if (requiredRole === UserRole.ADMIN) {
      return {
        text: "Admin Only",
        icon: <Lock className="w-4 h-4" />,
        color: "text-red-500 bg-red-50 border-red-200"
      };
    }
    
    if (requiredRole === UserRole.SUBSCRIBER || requiredPermissions?.some(p => p.includes('ai_assistant'))) {
      return {
        text: "Premium Feature",
        icon: <Crown className="w-4 h-4" />,
        color: "text-yellow-600 bg-yellow-50 border-yellow-200"
      };
    }

    return {
      text: "Restricted",
      icon: <AlertTriangle className="w-4 h-4" />,
      color: "text-orange-500 bg-orange-50 border-orange-200"
    };
  };

  const { text, icon, color } = getPromptInfo();

  return (
    <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full border text-sm font-medium ${color}`}>
      {icon}
      {text}
    </div>
  );
};

const BlockUpgradePrompt: React.FC<UpgradePromptProps> = ({ requiredRole, requiredPermissions }) => {
  const getPromptInfo = () => {
    if (requiredRole === UserRole.ADMIN) {
      return {
        title: "Administrator Access Required",
        description: "This feature is only available to administrators.",
        icon: <Lock className="w-8 h-8 text-red-500" />,
        showUpgrade: false,
        bgColor: "bg-red-50 border-red-200"
      };
    }
    
    if (requiredRole === UserRole.SUBSCRIBER || requiredPermissions?.some(p => p.includes('ai_assistant'))) {
      return {
        title: "Premium Feature",
        description: "Upgrade to unlock this feature",
        icon: <Crown className="w-8 h-8 text-yellow-500" />,
        showUpgrade: true,
        bgColor: "bg-yellow-50 border-yellow-200"
      };
    }

    return {
      title: "Access Restricted",
      description: "You don't have permission to access this feature.",
      icon: <AlertTriangle className="w-8 h-8 text-orange-500" />,
      showUpgrade: false,
      bgColor: "bg-orange-50 border-orange-200"
    };
  };

  const { title, description, icon, showUpgrade, bgColor } = getPromptInfo();

  return (
    <div className={`p-6 rounded-lg border ${bgColor} text-center`}>
      <div className="flex justify-center mb-4">
        {icon}
      </div>
      
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        {title}
      </h3>
      
      <p className="text-gray-600 mb-4">
        {description}
      </p>

      {showUpgrade && (
        <button
          onClick={() => window.location.href = '/subscribe'}
          className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 transform hover:scale-105"
        >
          Upgrade Now
        </button>
      )}
    </div>
  );
};

// Higher-order component for wrapping components with permission checks
export const withPermissions = <P extends object>(
  Component: React.ComponentType<P>,
  requiredRole?: UserRole,
  requiredPermissions?: Permission[]
) => {
  return (props: P) => (
    <PermissionGuard 
      requiredRole={requiredRole} 
      requiredPermissions={requiredPermissions}
    >
      <Component {...props} />
    </PermissionGuard>
  );
};

// Hook for checking permissions in components
export const usePermissions = () => {
  const { hasPermission, getUserRole, isAdmin, isSubscriber, canAccessRoute } = useUserStore();
  
  return {
    hasPermission,
    getUserRole,
    isAdmin,
    isSubscriber,
    canAccessRoute,
    checkRole: (role: UserRole) => {
      const userRole = getUserRole();
      if (!userRole) return false;
      
      const roleHierarchy = [UserRole.USER, UserRole.SUBSCRIBER, UserRole.ADMIN];
      const userRoleIndex = roleHierarchy.indexOf(userRole);
      const requiredRoleIndex = roleHierarchy.indexOf(role);
      
      return userRoleIndex >= requiredRoleIndex;
    }
  };
};

export default PermissionGuard;
