// Cashfree integration utility for FastCodr
// Handles plan creation, subscription session, and status check
// Uses fetch for REST API calls

const CASHFREE_BASE_URL = import.meta.env.VITE_CASHFREE_BASE_URL || 'https://sandbox.cashfree.com/pg';
const CASHFREE_CLIENT_ID = import.meta.env.VITE_CASHFREE_CLIENT_ID || '';
const CASHFREE_CLIENT_SECRET = import.meta.env.VITE_CASHFREE_CLIENT_SECRET || '';
const CASHFREE_PLAN_ID = import.meta.env.VITE_CASHFREE_PLAN_ID || 'fastcodr_premium_monthly';

// Helper to get access token
export async function getCashfreeToken() {
  const res = await fetch(`${CASHFREE_BASE_URL}/auth/api/v2/oauth`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-client-id': CASHFREE_CLIENT_ID,
      'x-client-secret': CASHFREE_CLIENT_SECRET,
    },
  });
  if (!res.ok) throw new Error('Failed to get Cashfree token');
  const data = await res.json();
  return data.access_token;
}

// Create or ensure plan exists
export async function createPlan() {
  const token = await getCashfreeToken();

  const planData = {
    plan_id: CASHFREE_PLAN_ID,
    plan_name: 'FastCodr Premium Monthly',
    plan_type: 'PERIODIC',
    plan_currency: 'USD',
    plan_amount: 1000, // $10.00 in cents
    plan_interval_type: 'MONTH',
    plan_intervals: 1,
    plan_description: 'FastCodr Premium subscription - $10/month',
  };

  try {
    const res = await fetch(`${CASHFREE_BASE_URL}/plans`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'x-api-version': '2022-09-01',
      },
      body: JSON.stringify(planData),
    });

    if (!res.ok) {
      const errorData = await res.json();
      // Plan might already exist
      if (errorData.code === 'plan_already_exists') {
        return { plan_id: CASHFREE_PLAN_ID };
      }
      throw new Error(errorData.message || 'Failed to create plan');
    }

    return await res.json();
  } catch (error: any) {
    console.error('Plan creation error:', error);
    return { error: error.message };
  }
}

// Create a subscription link for the user
export async function createSubscriptionLink(user: { email: string, name: string }) {
  try {
    // Ensure plan exists first
    await createPlan();

    const token = await getCashfreeToken();

    const subscriptionData = {
      subscription_id: `fastcodr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      plan_id: CASHFREE_PLAN_ID,
      customer_details: {
        customer_id: user.email.replace('@', '_at_'), // Cashfree doesn't allow @ in customer_id
        customer_email: user.email,
        customer_name: user.name,
        customer_phone: '9999999999', // Required field
      },
      subscription_meta: {
        return_url: `${window.location.origin}/subscribe/success`,
        notify_url: `${window.location.origin}/api/cashfree/webhook`,
      },
    };

    const res = await fetch(`${CASHFREE_BASE_URL}/subscriptions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'x-api-version': '2022-09-01',
      },
      body: JSON.stringify(subscriptionData),
    });

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.message || 'Failed to create subscription');
    }

    const data = await res.json();
    return {
      subscription_link: data.subscription_link,
      subscription_id: data.subscription_id,
    };
  } catch (error: any) {
    console.error('Cashfree subscription error:', error);
    return { error: error.message };
  }
}

// Verify subscription status
export async function verifySubscription(subscriptionId: string) {
  try {
    const token = await getCashfreeToken();

    const res = await fetch(`${CASHFREE_BASE_URL}/subscriptions/${subscriptionId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'x-api-version': '2022-09-01',
      },
    });

    if (!res.ok) {
      throw new Error('Failed to verify subscription');
    }

    return await res.json();
  } catch (error: any) {
    console.error('Subscription verification error:', error);
    return { error: error.message };
  }
}

// Cancel subscription
export async function cancelSubscription(subscriptionId: string) {
  try {
    const token = await getCashfreeToken();

    const res = await fetch(`${CASHFREE_BASE_URL}/subscriptions/${subscriptionId}/cancel`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'x-api-version': '2022-09-01',
      },
    });

    if (!res.ok) {
      throw new Error('Failed to cancel subscription');
    }

    return await res.json();
  } catch (error: any) {
    console.error('Subscription cancellation error:', error);
    return { error: error.message };
  }
}

// Check subscription status (MVP: store in localStorage)
export function setSubscriptionStatus(status: boolean, subscriptionId?: string) {
  localStorage.setItem('fastcodr_subscription', status ? 'active' : 'inactive');
  if (subscriptionId) {
    localStorage.setItem('fastcodr_subscription_id', subscriptionId);
  }
}

export function getSubscriptionStatus(): boolean {
  return localStorage.getItem('fastcodr_subscription') === 'active';
}

export function getSubscriptionId(): string | null {
  return localStorage.getItem('fastcodr_subscription_id');
}

export function clearSubscriptionData() {
  localStorage.removeItem('fastcodr_subscription');
  localStorage.removeItem('fastcodr_subscription_id');
}

// For support link
export function getSupportEmail() {
  return '<EMAIL>';
}
