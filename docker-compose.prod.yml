version: '3.8'

services:
  # FastCodr Backend (Next.js API)
  backend:
    build:
      context: ./apps/fastcodr-next
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
    container_name: fastcodr-backend-prod
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - HOSTNAME=0.0.0.0
    env_file:
      - ./apps/fastcodr-next/.env.production
    depends_on:
      - mongodb
      - redis
    restart: unless-stopped
    networks:
      - fastcodr-network
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # FastCodr Frontend (React + Vite)
  frontend:
    build:
      context: ./apps/fastcodr-client
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
    container_name: fastcodr-frontend-prod
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - fastcodr-network
    volumes:
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: fastcodr-mongodb-prod
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME:-fastcodr}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD:-your_secure_password}
      - MONGO_INITDB_DATABASE=fastcodr
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
      - ./mongodb-init:/docker-entrypoint-initdb.d:ro
      - ./backups:/backups
    restart: unless-stopped
    networks:
      - fastcodr-network
    command: mongod --auth --bind_ip_all
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: fastcodr-redis-prod
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-your_redis_password}
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    restart: unless-stopped
    networks:
      - fastcodr-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Load Balancer and SSL Termination
  nginx:
    image: nginx:alpine
    container_name: fastcodr-nginx-prod
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    networks:
      - fastcodr-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: fastcodr-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - fastcodr-network

  # Grafana for monitoring dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: fastcodr-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    restart: unless-stopped
    networks:
      - fastcodr-network

volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  fastcodr-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
