# FastCodr Deployment Script for Windows PowerShell
# Version: 2.0
# Description: Complete deployment automation for FastCodr application

param(
    [string]$Environment = "production",
    [switch]$SkipBuild = $false,
    [switch]$SkipTests = $false,
    [switch]$OpenBrowser,
    [switch]$Verbose = $false
)

Write-Host "🚀 Starting FastCodr deployment..." -ForegroundColor Blue
Write-Host "Environment: $Environment" -ForegroundColor Cyan
Write-Host "Skip Build: $SkipBuild" -ForegroundColor Cyan
Write-Host "Skip Tests: $SkipTests" -ForegroundColor Cyan

# Function to print colored output
function Write-Status {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
    if ($Verbose) { Write-Host "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - $Message" }
}

function Write-Success {
    param($Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Step {
    param($Step, $Total, $Message)
    Write-Host "[$Step/$Total] $Message" -ForegroundColor Magenta
}

# Check if Docker is installed
try {
    docker --version | Out-Null
    Write-Success "Docker is installed"
} catch {
    Write-Error "Docker is not installed. Please install Docker Desktop first."
    exit 1
}

# Check if Docker Compose is installed
try {
    docker-compose --version | Out-Null
    Write-Success "Docker Compose is installed"
} catch {
    Write-Error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
}

# Environment setup
Write-Status "Setting up environment..."

# Check if production environment files exist
if (-not (Test-Path "apps/fastcodr-next/.env.production")) {
    Write-Warning "Backend production environment file not found. Creating from template..."
    Copy-Item "apps/fastcodr-next/.env.example" "apps/fastcodr-next/.env.production"
}

if (-not (Test-Path "apps/fastcodr-client/.env.production")) {
    Write-Warning "Frontend production environment file not found. Creating from template..."
    Copy-Item "apps/fastcodr-client/.env.example" "apps/fastcodr-client/.env.production"
}

# Build and deploy
Write-Status "Building Docker images..."
docker-compose build

if ($LASTEXITCODE -ne 0) {
    Write-Error "Docker build failed"
    exit 1
}

Write-Status "Starting services..."
docker-compose up -d

if ($LASTEXITCODE -ne 0) {
    Write-Error "Failed to start services"
    exit 1
}

# Wait for services to be ready
Write-Status "Waiting for services to start..."
Start-Sleep -Seconds 30

# Health checks
Write-Status "Performing health checks..."

# Check backend health
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001" -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Success "Backend is running on http://localhost:3001"
    }
} catch {
    Write-Error "Backend health check failed: $($_.Exception.Message)"
}

# Check frontend health
try {
    $response = Invoke-WebRequest -Uri "http://localhost:80" -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Success "Frontend is running on http://localhost:80"
    }
} catch {
    Write-Error "Frontend health check failed: $($_.Exception.Message)"
}

Write-Success "🎉 FastCodr deployment completed!"
Write-Status "Access your application at:"
Write-Status "  Frontend: http://localhost"
Write-Status "  Backend API: http://localhost:3001"
Write-Status "  MongoDB: localhost:27017"

Write-Status "To view logs, run: docker-compose logs -f"
Write-Status "To stop services, run: docker-compose down"

# Open browser to the application
Write-Status "Opening FastCodr in your default browser..."
Start-Process "http://localhost"
