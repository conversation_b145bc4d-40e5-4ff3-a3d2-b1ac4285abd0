# Required Dependencies for Code/Visual Editor Upgrade

## Monaco Editor Dependencies
```bash
npm install monaco-editor @monaco-editor/react
npm install --save-dev monaco-editor-webpack-plugin
```

## React DnD Dependencies (for Visual Builder)
```bash
npm install react-dnd react-dnd-html5-backend
npm install @types/react-dnd @types/react-dnd-html5-backend
```

## Syntax Highlighting Dependencies
```bash
npm install react-syntax-highlighter
npm install @types/react-syntax-highlighter
```

## Additional UI Dependencies
```bash
npm install react-resizable-panels
npm install @types/react-resizable-panels
```

## Vite Configuration for Monaco Editor

Add to `vite.config.ts`:

```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import monacoEditorPlugin from 'vite-plugin-monaco-editor'

export default defineConfig({
  plugins: [
    react(),
    monacoEditorPlugin({
      languageWorkers: ['editorWorkerService', 'typescript', 'json', 'html', 'css']
    })
  ],
  optimizeDeps: {
    include: ['monaco-editor']
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          monaco: ['monaco-editor']
        }
      }
    }
  }
})
```

## Installation Commands

Run these commands in the `apps/fastcodr-client` directory:

```bash
# Core dependencies
npm install monaco-editor @monaco-editor/react react-dnd react-dnd-html5-backend react-syntax-highlighter

# Development dependencies  
npm install --save-dev @types/react-dnd @types/react-dnd-html5-backend @types/react-syntax-highlighter vite-plugin-monaco-editor

# Optional: If react-resizable-panels is not already installed
npm install react-resizable-panels
```

## Notes

1. **Monaco Editor**: Provides VS Code-like editing experience
2. **React DnD**: Enables drag-and-drop functionality for Visual Builder
3. **Syntax Highlighter**: Used for code display in AI responses
4. **Vite Plugin**: Optimizes Monaco Editor bundling for production

These dependencies are required for the enhanced editor features to work properly.
