import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { toast } from 'react-hot-toast';

// Project interfaces
export interface ProjectFile {
  id: string;
  name: string;
  content: string;
  language: string;
  path: string;
  size: number;
  lastModified: Date;
  isDirectory: boolean;
  children?: ProjectFile[];
}

export interface Project {
  id: string;
  name: string;
  description: string;
  userId: string;
  files: ProjectFile[];
  settings: {
    theme: 'light' | 'dark';
    fontSize: number;
    tabSize: number;
    wordWrap: boolean;
    minimap: boolean;
    autoSave: boolean;
    preferredEditor: 'codemirror' | 'monaco' | 'visual';
  };
  metadata: {
    framework: string;
    language: string;
    template: string;
    dependencies: string[];
    buildCommand?: string;
    startCommand?: string;
  };
  tags: string[];
  isPublic: boolean;
  isTemplate: boolean;
  collaborators: string[];
  viewCount: number;
  forkCount: number;
  starCount: number;
  status: 'draft' | 'published' | 'archived';
  version: string;
  isDraft: boolean;
  draftSavedAt?: Date;
  totalSize: number;
  fileCount: number;
  createdAt: Date;
  updatedAt: Date;
  lastOpenedAt?: Date;
}

export interface ProjectTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  framework: string;
  language: string;
  thumbnail: string;
  isPremium: boolean;
  tags: string[];
  usageCount: number;
  rating: number;
}

interface ProjectState {
  // Current project
  currentProject: Project | null;
  isLoading: boolean;
  isSaving: boolean;
  isAutoSaving: boolean;
  lastSaved: Date | null;
  hasUnsavedChanges: boolean;
  
  // Projects list
  projects: Project[];
  templates: ProjectTemplate[];
  
  // Auto-save settings
  autoSaveEnabled: boolean;
  autoSaveInterval: number;
  
  // Actions
  setCurrentProject: (project: Project | null) => void;
  updateCurrentProject: (updates: Partial<Project>) => void;
  updateProjectFile: (fileId: string, content: string) => void;
  addProjectFile: (file: Omit<ProjectFile, 'id' | 'lastModified'>) => void;
  removeProjectFile: (fileId: string) => void;
  
  // API actions
  loadProjects: () => Promise<void>;
  loadProject: (id: string) => Promise<void>;
  saveProject: (project?: Project) => Promise<void>;
  autoSaveProject: () => Promise<void>;
  createProject: (data: { name: string; description?: string; template?: string }) => Promise<Project>;
  deleteProject: (id: string) => Promise<void>;
  
  // Templates
  loadTemplates: () => Promise<void>;
  createFromTemplate: (templateId: string, name: string, description?: string) => Promise<Project>;
  
  // Settings
  updateAutoSaveSettings: (enabled: boolean, interval?: number) => void;
  markAsUnsaved: () => void;
  markAsSaved: () => void;
}

const useProjectStore = create<ProjectState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentProject: null,
      isLoading: false,
      isSaving: false,
      isAutoSaving: false,
      lastSaved: null,
      hasUnsavedChanges: false,
      projects: [],
      templates: [],
      autoSaveEnabled: true,
      autoSaveInterval: 30000, // 30 seconds
      
      // Basic setters
      setCurrentProject: (project) => {
        set({ 
          currentProject: project,
          hasUnsavedChanges: false,
          lastSaved: project ? new Date() : null
        });
      },
      
      updateCurrentProject: (updates) => {
        const current = get().currentProject;
        if (current) {
          set({ 
            currentProject: { ...current, ...updates },
            hasUnsavedChanges: true
          });
        }
      },
      
      updateProjectFile: (fileId, content) => {
        const current = get().currentProject;
        if (!current) return;
        
        const updateFileInTree = (files: ProjectFile[]): ProjectFile[] => {
          return files.map(file => {
            if (file.id === fileId) {
              return {
                ...file,
                content,
                size: content.length,
                lastModified: new Date()
              };
            }
            if (file.children) {
              return {
                ...file,
                children: updateFileInTree(file.children)
              };
            }
            return file;
          });
        };
        
        const updatedFiles = updateFileInTree(current.files);
        set({
          currentProject: {
            ...current,
            files: updatedFiles,
            updatedAt: new Date()
          },
          hasUnsavedChanges: true
        });
      },
      
      addProjectFile: (fileData) => {
        const current = get().currentProject;
        if (!current) return;
        
        const newFile: ProjectFile = {
          ...fileData,
          id: `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          lastModified: new Date()
        };
        
        set({
          currentProject: {
            ...current,
            files: [...current.files, newFile],
            fileCount: current.fileCount + 1,
            updatedAt: new Date()
          },
          hasUnsavedChanges: true
        });
      },
      
      removeProjectFile: (fileId) => {
        const current = get().currentProject;
        if (!current) return;
        
        const removeFileFromTree = (files: ProjectFile[]): ProjectFile[] => {
          return files.filter(file => {
            if (file.id === fileId) return false;
            if (file.children) {
              file.children = removeFileFromTree(file.children);
            }
            return true;
          });
        };
        
        const updatedFiles = removeFileFromTree(current.files);
        set({
          currentProject: {
            ...current,
            files: updatedFiles,
            fileCount: current.fileCount - 1,
            updatedAt: new Date()
          },
          hasUnsavedChanges: true
        });
      },
      
      // API actions
      loadProjects: async () => {
        set({ isLoading: true });
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/projects`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (!response.ok) {
            throw new Error('Failed to load projects');
          }
          
          const data = await response.json();
          set({ 
            projects: data.data.projects,
            isLoading: false 
          });
        } catch (error) {
          console.error('Error loading projects:', error);
          toast.error('Failed to load projects');
          set({ isLoading: false });
        }
      },
      
      loadProject: async (id) => {
        set({ isLoading: true });
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/projects/${id}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (!response.ok) {
            throw new Error('Failed to load project');
          }
          
          const data = await response.json();
          set({ 
            currentProject: data.data,
            isLoading: false,
            hasUnsavedChanges: false,
            lastSaved: new Date()
          });
        } catch (error) {
          console.error('Error loading project:', error);
          toast.error('Failed to load project');
          set({ isLoading: false });
        }
      },
      
      saveProject: async (project) => {
        const current = project || get().currentProject;
        if (!current) return;
        
        set({ isSaving: true });
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/projects/${current.id}`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              name: current.name,
              description: current.description,
              files: current.files,
              settings: current.settings,
              metadata: current.metadata,
              tags: current.tags,
              isPublic: current.isPublic,
              status: 'published'
            })
          });
          
          if (!response.ok) {
            throw new Error('Failed to save project');
          }
          
          const data = await response.json();
          set({ 
            currentProject: data.data,
            isSaving: false,
            hasUnsavedChanges: false,
            lastSaved: new Date()
          });
          
          toast.success('Project saved successfully');
        } catch (error) {
          console.error('Error saving project:', error);
          toast.error('Failed to save project');
          set({ isSaving: false });
        }
      },
      
      autoSaveProject: async () => {
        const current = get().currentProject;
        if (!current || !get().hasUnsavedChanges || !get().autoSaveEnabled) return;
        
        set({ isAutoSaving: true });
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/projects/${current.id}/autosave`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              files: current.files,
              settings: current.settings
            })
          });
          
          if (response.ok) {
            set({ 
              isAutoSaving: false,
              lastSaved: new Date()
            });
          }
        } catch (error) {
          console.error('Auto-save failed:', error);
          set({ isAutoSaving: false });
        }
      },
      
      createProject: async (data) => {
        set({ isLoading: true });
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/projects`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
          });
          
          if (!response.ok) {
            throw new Error('Failed to create project');
          }
          
          const result = await response.json();
          const newProject = result.data;
          
          set({ 
            currentProject: newProject,
            projects: [newProject, ...get().projects],
            isLoading: false,
            hasUnsavedChanges: false,
            lastSaved: new Date()
          });
          
          toast.success('Project created successfully');
          return newProject;
        } catch (error) {
          console.error('Error creating project:', error);
          toast.error('Failed to create project');
          set({ isLoading: false });
          throw error;
        }
      },
      
      deleteProject: async (id) => {
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/projects/${id}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (!response.ok) {
            throw new Error('Failed to delete project');
          }
          
          set({ 
            projects: get().projects.filter(p => p.id !== id),
            currentProject: get().currentProject?.id === id ? null : get().currentProject
          });
          
          toast.success('Project deleted successfully');
        } catch (error) {
          console.error('Error deleting project:', error);
          toast.error('Failed to delete project');
        }
      },
      
      loadTemplates: async () => {
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/projects/templates`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (!response.ok) {
            throw new Error('Failed to load templates');
          }
          
          const data = await response.json();
          set({ templates: data.data.templates });
        } catch (error) {
          console.error('Error loading templates:', error);
          toast.error('Failed to load templates');
        }
      },
      
      createFromTemplate: async (templateId, name, description) => {
        set({ isLoading: true });
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/projects/templates`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              templateId,
              projectName: name,
              projectDescription: description
            })
          });
          
          if (!response.ok) {
            throw new Error('Failed to create project from template');
          }
          
          const result = await response.json();
          const newProject = result.data;
          
          set({ 
            currentProject: newProject,
            projects: [newProject, ...get().projects],
            isLoading: false,
            hasUnsavedChanges: false,
            lastSaved: new Date()
          });
          
          toast.success(`Project created from template`);
          return newProject;
        } catch (error) {
          console.error('Error creating project from template:', error);
          toast.error('Failed to create project from template');
          set({ isLoading: false });
          throw error;
        }
      },
      
      updateAutoSaveSettings: (enabled, interval) => {
        set({ 
          autoSaveEnabled: enabled,
          autoSaveInterval: interval || get().autoSaveInterval
        });
      },
      
      markAsUnsaved: () => set({ hasUnsavedChanges: true }),
      markAsSaved: () => set({ hasUnsavedChanges: false, lastSaved: new Date() })
    }),
    {
      name: 'fastcodr-project-store',
      partialize: (state) => ({
        autoSaveEnabled: state.autoSaveEnabled,
        autoSaveInterval: state.autoSaveInterval
      })
    }
  )
);

export default useProjectStore;
