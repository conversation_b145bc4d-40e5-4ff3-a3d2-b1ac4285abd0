import jwt from 'jsonwebtoken';
import { NextRequest } from 'next/server';

// User roles enum
export enum UserRole {
  USER = "user",
  SUBSCRIBER = "subscriber", 
  ADMIN = "admin"
}

// Permissions enum
export enum Permission {
  // Basic permissions
  VIEW_EDITOR = "view_editor",
  SAVE_PROJECT = "save_project",
  
  // Subscriber permissions
  AI_ASSISTANT = "ai_assistant",
  UNLIMITED_PROJECTS = "unlimited_projects",
  PREMIUM_TEMPLATES = "premium_templates",
  EXPORT_PROJECTS = "export_projects",
  
  // Admin permissions
  ADMIN_DASHBOARD = "admin_dashboard",
  MANAGE_USERS = "manage_users",
  VIEW_ANALYTICS = "view_analytics",
  MANAGE_SUBSCRIPTIONS = "manage_subscriptions"
}

// Role permissions mapping
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.USER]: [
    Permission.VIEW_EDITOR,
    Permission.SAVE_PROJECT
  ],
  [UserRole.SUBSCRIBER]: [
    Permission.VIEW_EDITOR,
    Permission.SAVE_PROJECT,
    Permission.AI_ASSISTANT,
    Permission.UNLIMITED_PROJECTS,
    Permission.PREMIUM_TEMPLATES,
    Permission.EXPORT_PROJECTS
  ],
  [UserRole.ADMIN]: [
    Permission.VIEW_EDITOR,
    Permission.SAVE_PROJECT,
    Permission.AI_ASSISTANT,
    Permission.UNLIMITED_PROJECTS,
    Permission.PREMIUM_TEMPLATES,
    Permission.EXPORT_PROJECTS,
    Permission.ADMIN_DASHBOARD,
    Permission.MANAGE_USERS,
    Permission.VIEW_ANALYTICS,
    Permission.MANAGE_SUBSCRIPTIONS
  ]
};

// Usage limits per role
export interface UsageLimits {
  projectsPerMonth: number;
  aiCallsPerMonth: number;
  storageLimit: number; // in MB
  collaborators: number;
}

export const ROLE_LIMITS: Record<UserRole, UsageLimits> = {
  [UserRole.USER]: {
    projectsPerMonth: 3,
    aiCallsPerMonth: 50,
    storageLimit: 100,
    collaborators: 0
  },
  [UserRole.SUBSCRIBER]: {
    projectsPerMonth: 50,
    aiCallsPerMonth: 1000,
    storageLimit: 1000,
    collaborators: 5
  },
  [UserRole.ADMIN]: {
    projectsPerMonth: -1, // unlimited
    aiCallsPerMonth: -1, // unlimited
    storageLimit: -1, // unlimited
    collaborators: -1 // unlimited
  }
};

// JWT payload interface
export interface JWTPayload {
  userId: string;
  email: string;
  role: UserRole;
  permissions: Permission[];
  iat: number;
  exp: number;
}

// User interface for database
export interface User {
  id: string;
  email: string;
  username: string;
  role: UserRole;
  permissions: Permission[];
  usageLimits: UsageLimits;
  currentUsage: {
    projectsThisMonth: number;
    aiCallsThisMonth: number;
    storageUsed: number;
    resetDate: Date;
  };
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  subscriptionId?: string;
  subscriptionStatus?: string;
}

// JWT utility functions
export const generateToken = (user: User): string => {
  const payload: Omit<JWTPayload, 'iat' | 'exp'> = {
    userId: user.id,
    email: user.email,
    role: user.role,
    permissions: user.permissions
  };

  return jwt.sign(payload, process.env.JWT_SECRET!, {
    expiresIn: '7d',
    issuer: 'fastcodr',
    audience: 'fastcodr-users'
  });
};

export const verifyToken = (token: string): JWTPayload | null => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!, {
      issuer: 'fastcodr',
      audience: 'fastcodr-users'
    }) as JWTPayload;
    
    return decoded;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
};

export const extractTokenFromRequest = (request: NextRequest): string | null => {
  // Check Authorization header
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Check cookies
  const token = request.cookies.get('token')?.value;
  if (token) {
    return token;
  }

  return null;
};

// Permission checking functions
export const hasPermission = (userRole: UserRole, permission: Permission): boolean => {
  return ROLE_PERMISSIONS[userRole]?.includes(permission) || false;
};

export const hasAnyPermission = (userRole: UserRole, permissions: Permission[]): boolean => {
  return permissions.some(permission => hasPermission(userRole, permission));
};

export const hasAllPermissions = (userRole: UserRole, permissions: Permission[]): boolean => {
  return permissions.every(permission => hasPermission(userRole, permission));
};

// Role hierarchy checking
export const hasRoleOrHigher = (userRole: UserRole, requiredRole: UserRole): boolean => {
  const roleHierarchy = [UserRole.USER, UserRole.SUBSCRIBER, UserRole.ADMIN];
  const userRoleIndex = roleHierarchy.indexOf(userRole);
  const requiredRoleIndex = roleHierarchy.indexOf(requiredRole);
  
  return userRoleIndex >= requiredRoleIndex;
};

// Middleware for protecting API routes
export const requireAuth = (requiredRole?: UserRole, requiredPermissions?: Permission[]) => {
  return async (request: NextRequest): Promise<{ user: JWTPayload } | Response> => {
    const token = extractTokenFromRequest(request);
    
    if (!token) {
      return new Response(JSON.stringify({ error: 'Authentication required' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const payload = verifyToken(token);
    if (!payload) {
      return new Response(JSON.stringify({ error: 'Invalid or expired token' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check role requirement
    if (requiredRole && !hasRoleOrHigher(payload.role, requiredRole)) {
      return new Response(JSON.stringify({ error: 'Insufficient permissions' }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check specific permissions
    if (requiredPermissions && !hasAllPermissions(payload.role, requiredPermissions)) {
      return new Response(JSON.stringify({ error: 'Missing required permissions' }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return { user: payload };
  };
};

// Helper function to get user permissions
export const getUserPermissions = (role: UserRole): Permission[] => {
  return ROLE_PERMISSIONS[role] || [];
};

// Helper function to get user limits
export const getUserLimits = (role: UserRole): UsageLimits => {
  return ROLE_LIMITS[role];
};

// Function to upgrade user role (for subscription changes)
export const upgradeUserRole = (currentRole: UserRole, subscriptionActive: boolean): UserRole => {
  if (subscriptionActive && currentRole === UserRole.USER) {
    return UserRole.SUBSCRIBER;
  }
  if (!subscriptionActive && currentRole === UserRole.SUBSCRIBER) {
    return UserRole.USER;
  }
  return currentRole;
};

// Function to check if user can perform action based on usage limits
export const canPerformAction = (
  userRole: UserRole, 
  currentUsage: User['currentUsage'], 
  action: 'project' | 'aiCall' | 'storage'
): boolean => {
  const limits = getUserLimits(userRole);
  
  switch (action) {
    case 'project':
      return limits.projectsPerMonth === -1 || currentUsage.projectsThisMonth < limits.projectsPerMonth;
    case 'aiCall':
      return limits.aiCallsPerMonth === -1 || currentUsage.aiCallsThisMonth < limits.aiCallsPerMonth;
    case 'storage':
      return limits.storageLimit === -1 || currentUsage.storageUsed < limits.storageLimit;
    default:
      return false;
  }
};
