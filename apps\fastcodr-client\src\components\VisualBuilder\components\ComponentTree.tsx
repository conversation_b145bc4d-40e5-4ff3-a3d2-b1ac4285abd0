import React from 'react';
import { <PERSON><PERSON><PERSON>R<PERSON>, ChevronDown, Trash2, Eye } from 'lucide-react';
import { UIComponent } from '../VisualBuilder';

interface ComponentTreeProps {
  components: UIComponent[];
  selectedId: string | null;
  onSelect: (id: string) => void;
  onRemove: (id: string) => void;
  level?: number;
}

const ComponentTree: React.FC<ComponentTreeProps> = ({
  components,
  selectedId,
  onSelect,
  onRemove,
  level = 0
}) => {
  const [expandedItems, setExpandedItems] = React.useState<Set<string>>(new Set());

  const toggleExpanded = (id: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  if (components.length === 0) {
    return (
      <div className="text-sm text-gray-500 italic">
        No components yet. Drag components from the palette above.
      </div>
    );
  }

  return (
    <div className="space-y-1">
      {components.map((component) => (
        <ComponentTreeItem
          key={component.id}
          component={component}
          selectedId={selectedId}
          onSelect={onSelect}
          onRemove={onRemove}
          level={level}
          isExpanded={expandedItems.has(component.id)}
          onToggleExpanded={() => toggleExpanded(component.id)}
        />
      ))}
    </div>
  );
};

interface ComponentTreeItemProps {
  component: UIComponent;
  selectedId: string | null;
  onSelect: (id: string) => void;
  onRemove: (id: string) => void;
  level: number;
  isExpanded: boolean;
  onToggleExpanded: () => void;
}

const ComponentTreeItem: React.FC<ComponentTreeItemProps> = ({
  component,
  selectedId,
  onSelect,
  onRemove,
  level,
  isExpanded,
  onToggleExpanded
}) => {
  const hasChildren = component.children && component.children.length > 0;
  const isSelected = selectedId === component.id;

  const getComponentLabel = () => {
    const baseLabel = component.type;
    if (component.props.children && typeof component.props.children === 'string') {
      const text = component.props.children.substring(0, 20);
      return `${baseLabel} "${text}${component.props.children.length > 20 ? '...' : ''}"`;
    }
    return baseLabel;
  };

  return (
    <div>
      <div
        className={`
          flex items-center gap-1 px-2 py-1 rounded text-sm cursor-pointer group
          ${isSelected ? 'bg-blue-100 text-blue-900' : 'hover:bg-gray-100'}
        `}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={() => onSelect(component.id)}
      >
        {hasChildren ? (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleExpanded();
            }}
            className="p-0.5 hover:bg-gray-200 rounded"
          >
            {isExpanded ? (
              <ChevronDown className="w-3 h-3" />
            ) : (
              <ChevronRight className="w-3 h-3" />
            )}
          </button>
        ) : (
          <div className="w-4" />
        )}

        <Eye className="w-3 h-3 text-gray-400" />
        
        <span className="flex-1 truncate">{getComponentLabel()}</span>
        
        <button
          onClick={(e) => {
            e.stopPropagation();
            onRemove(component.id);
          }}
          className="opacity-0 group-hover:opacity-100 p-0.5 hover:bg-red-100 hover:text-red-600 rounded transition-all"
          title="Remove component"
        >
          <Trash2 className="w-3 h-3" />
        </button>
      </div>

      {hasChildren && isExpanded && (
        <ComponentTree
          components={component.children!}
          selectedId={selectedId}
          onSelect={onSelect}
          onRemove={onRemove}
          level={level + 1}
        />
      )}
    </div>
  );
};

export default ComponentTree;
