import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, UserRole, Permission, canPerformAction } from '@/lib/auth';
import { 
  Project, 
  CreateProjectRequest, 
  ProjectFilters,
  DEFAULT_PROJECT_SETTINGS,
  DEFAULT_PROJECT_METADATA,
  calculateProjectSize,
  countProjectFiles
} from '@/lib/models/Project';

// GET /api/projects - Get user's projects with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(UserRole.USER, [Permission.SAVE_PROJECT])(request);
    
    if (authResult instanceof Response) {
      return authResult;
    }

    const { user } = authResult;
    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const filters: ProjectFilters = {
      userId: user.userId,
      framework: searchParams.get('framework') || undefined,
      language: searchParams.get('language') || undefined,
      tags: searchParams.get('tags')?.split(',') || undefined,
      isPublic: searchParams.get('isPublic') === 'true' ? true : undefined,
      isTemplate: searchParams.get('isTemplate') === 'true' ? true : undefined,
      status: (searchParams.get('status') as Project['status']) || undefined,
      search: searchParams.get('search') || undefined,
      sortBy: (searchParams.get('sortBy') as any) || 'updatedAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
      limit: parseInt(searchParams.get('limit') || '20'),
      offset: parseInt(searchParams.get('offset') || '0')
    };

    // Mock data - replace with actual database query
    const mockProjects: Project[] = [
      {
        id: 'proj_1',
        name: 'My React App',
        description: 'A beautiful React application',
        userId: user.userId,
        files: [],
        settings: DEFAULT_PROJECT_SETTINGS,
        metadata: { ...DEFAULT_PROJECT_METADATA, framework: 'react' },
        tags: ['react', 'typescript'],
        isPublic: false,
        isTemplate: false,
        collaborators: [],
        viewCount: 15,
        forkCount: 2,
        starCount: 5,
        status: 'published',
        version: '1.0.0',
        isDraft: false,
        totalSize: 1024 * 50, // 50KB
        fileCount: 8,
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-20'),
        lastOpenedAt: new Date('2024-01-20')
      },
      {
        id: 'proj_2',
        name: 'Vue Dashboard',
        description: 'Admin dashboard built with Vue.js',
        userId: user.userId,
        files: [],
        settings: DEFAULT_PROJECT_SETTINGS,
        metadata: { ...DEFAULT_PROJECT_METADATA, framework: 'vue' },
        tags: ['vue', 'dashboard', 'admin'],
        isPublic: true,
        isTemplate: false,
        collaborators: [],
        viewCount: 32,
        forkCount: 7,
        starCount: 12,
        status: 'published',
        version: '2.1.0',
        isDraft: false,
        totalSize: 1024 * 120, // 120KB
        fileCount: 15,
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-18'),
        lastOpenedAt: new Date('2024-01-19')
      },
      {
        id: 'proj_3',
        name: 'API Server',
        description: 'Node.js REST API server',
        userId: user.userId,
        files: [],
        settings: DEFAULT_PROJECT_SETTINGS,
        metadata: { ...DEFAULT_PROJECT_METADATA, framework: 'nodejs', language: 'javascript' },
        tags: ['nodejs', 'api', 'backend'],
        isPublic: false,
        isTemplate: false,
        collaborators: [],
        viewCount: 8,
        forkCount: 1,
        starCount: 3,
        status: 'draft',
        version: '0.1.0',
        isDraft: true,
        draftSavedAt: new Date(),
        totalSize: 1024 * 30, // 30KB
        fileCount: 5,
        createdAt: new Date('2024-01-22'),
        updatedAt: new Date('2024-01-22')
      }
    ];

    // Apply filters (in real implementation, this would be done in database query)
    let filteredProjects = mockProjects.filter(project => {
      if (filters.framework && project.metadata.framework !== filters.framework) return false;
      if (filters.language && project.metadata.language !== filters.language) return false;
      if (filters.status && project.status !== filters.status) return false;
      if (filters.isPublic !== undefined && project.isPublic !== filters.isPublic) return false;
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        if (!project.name.toLowerCase().includes(searchLower) && 
            !project.description.toLowerCase().includes(searchLower)) return false;
      }
      if (filters.tags && filters.tags.length > 0) {
        if (!filters.tags.some(tag => project.tags.includes(tag))) return false;
      }
      return true;
    });

    // Apply sorting
    filteredProjects.sort((a, b) => {
      const aValue = a[filters.sortBy!] as any;
      const bValue = b[filters.sortBy!] as any;
      
      if (filters.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    // Apply pagination
    const total = filteredProjects.length;
    const projects = filteredProjects.slice(filters.offset, filters.offset! + filters.limit!);

    return NextResponse.json({
      success: true,
      data: {
        projects,
        pagination: {
          total,
          limit: filters.limit,
          offset: filters.offset,
          hasMore: filters.offset! + filters.limit! < total
        }
      }
    });

  } catch (error) {
    console.error('Error fetching projects:', error);
    return NextResponse.json(
      { error: 'Failed to fetch projects' },
      { status: 500 }
    );
  }
}

// POST /api/projects - Create a new project
export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(UserRole.USER, [Permission.SAVE_PROJECT])(request);
    
    if (authResult instanceof Response) {
      return authResult;
    }

    const { user } = authResult;

    // Check usage limits
    const mockCurrentUsage = {
      projectsThisMonth: 2, // This should come from database
      aiCallsThisMonth: 0,
      storageUsed: 0,
      resetDate: new Date()
    };

    if (!canPerformAction(user.role, mockCurrentUsage, 'project')) {
      return NextResponse.json({
        error: 'Project limit exceeded for your plan',
        upgradeRequired: user.role === UserRole.USER,
        currentUsage: mockCurrentUsage
      }, { status: 429 });
    }

    const createRequest: CreateProjectRequest = await request.json();

    // Validate required fields
    if (!createRequest.name || createRequest.name.trim().length === 0) {
      return NextResponse.json(
        { error: 'Project name is required' },
        { status: 400 }
      );
    }

    // Generate project ID
    const projectId = `proj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create project object
    const newProject: Project = {
      id: projectId,
      name: createRequest.name.trim(),
      description: createRequest.description || '',
      userId: user.userId,
      files: createRequest.files?.map(file => ({
        id: `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: file.name || 'untitled.txt',
        content: file.content || '',
        language: file.language || 'plaintext',
        path: file.path || `/${file.name || 'untitled.txt'}`,
        size: (file.content || '').length,
        lastModified: new Date(),
        isDirectory: file.isDirectory || false,
        children: file.children || undefined
      })) || [],
      settings: DEFAULT_PROJECT_SETTINGS,
      metadata: {
        ...DEFAULT_PROJECT_METADATA,
        framework: createRequest.framework || 'react',
        language: createRequest.language || 'typescript'
      },
      tags: [],
      isPublic: createRequest.isPublic || false,
      isTemplate: false,
      collaborators: [],
      viewCount: 0,
      forkCount: 0,
      starCount: 0,
      status: 'draft',
      version: '0.1.0',
      isDraft: true,
      totalSize: 0,
      fileCount: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Calculate project size and file count
    newProject.totalSize = calculateProjectSize(newProject.files);
    newProject.fileCount = countProjectFiles(newProject.files);

    // TODO: Save to database
    // await saveProjectToDatabase(newProject);

    // TODO: Increment user's project count
    // await incrementUserProjectCount(user.userId);

    return NextResponse.json({
      success: true,
      data: newProject,
      message: 'Project created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating project:', error);
    return NextResponse.json(
      { error: 'Failed to create project' },
      { status: 500 }
    );
  }
}
