import React, { useState, useEffect } from 'react';
import { X, Plus, <PERSON>rk<PERSON>, <PERSON>, Palette } from 'lucide-react';
import useProjectStore, { ProjectTemplate } from '@/stores/projectSlice';
import { toast } from 'react-hot-toast';

interface CreateProjectModalProps {
  onClose: () => void;
  onSuccess: () => void;
}

const CreateProjectModal: React.FC<CreateProjectModalProps> = ({ onClose, onSuccess }) => {
  const { 
    templates, 
    loadTemplates, 
    createProject, 
    createFromTemplate, 
    isLoading 
  } = useProjectStore();
  
  const [step, setStep] = useState<'method' | 'template' | 'details'>('method');
  const [selectedTemplate, setSelectedTemplate] = useState<ProjectTemplate | null>(null);
  const [projectData, setProjectData] = useState({
    name: '',
    description: '',
    framework: 'react',
    language: 'typescript'
  });

  useEffect(() => {
    loadTemplates();
  }, [loadTemplates]);

  const handleCreateBlank = () => {
    setStep('details');
    setSelectedTemplate(null);
  };

  const handleSelectTemplate = (template: ProjectTemplate) => {
    setSelectedTemplate(template);
    setProjectData(prev => ({
      ...prev,
      framework: template.framework,
      language: template.language
    }));
    setStep('details');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!projectData.name.trim()) {
      toast.error('Project name is required');
      return;
    }

    try {
      if (selectedTemplate) {
        await createFromTemplate(
          selectedTemplate.id,
          projectData.name,
          projectData.description
        );
      } else {
        await createProject({
          name: projectData.name,
          description: projectData.description,
          template: undefined,
          framework: projectData.framework,
          language: projectData.language
        });
      }
      
      onSuccess();
    } catch (error) {
      // Error is handled in the store
    }
  };

  const categorizedTemplates = templates.reduce((acc, template) => {
    if (!acc[template.category]) {
      acc[template.category] = [];
    }
    acc[template.category].push(template);
    return acc;
  }, {} as Record<string, ProjectTemplate[]>);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Create New Project
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Step 1: Choose Method */}
          {step === 'method' && (
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
                How would you like to start?
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Blank Project */}
                <div
                  onClick={handleCreateBlank}
                  className="p-6 border-2 border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-500 cursor-pointer transition-colors group"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center group-hover:bg-blue-100 dark:group-hover:bg-blue-900 transition-colors">
                      <Code className="w-6 h-6 text-gray-600 dark:text-gray-400 group-hover:text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white">
                        Blank Project
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Start from scratch
                      </p>
                    </div>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Create an empty project and build everything yourself. Perfect for custom setups and learning.
                  </p>
                </div>

                {/* Template Project */}
                <div
                  onClick={() => setStep('template')}
                  className="p-6 border-2 border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-500 cursor-pointer transition-colors group"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center group-hover:bg-blue-100 dark:group-hover:bg-blue-900 transition-colors">
                      <Palette className="w-6 h-6 text-gray-600 dark:text-gray-400 group-hover:text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white">
                        Use Template
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Start with a template
                      </p>
                    </div>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Choose from pre-built templates to get started quickly with best practices and common setups.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Choose Template */}
          {step === 'template' && (
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Choose a Template
                </h3>
                <button
                  onClick={() => setStep('method')}
                  className="text-blue-600 hover:text-blue-700 text-sm"
                >
                  ← Back
                </button>
              </div>

              <div className="space-y-6">
                {Object.entries(categorizedTemplates).map(([category, categoryTemplates]) => (
                  <div key={category}>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                      {category}
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {categoryTemplates.map((template) => (
                        <div
                          key={template.id}
                          onClick={() => handleSelectTemplate(template)}
                          className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-500 cursor-pointer transition-colors group"
                        >
                          <div className="flex items-center gap-3 mb-3">
                            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center text-white text-sm">
                              {template.framework.charAt(0).toUpperCase()}
                            </div>
                            <div className="flex-1">
                              <h5 className="font-medium text-gray-900 dark:text-white">
                                {template.name}
                              </h5>
                              {template.isPremium && (
                                <span className="inline-flex items-center gap-1 text-xs text-yellow-600 dark:text-yellow-400">
                                  <Sparkles className="w-3 h-3" />
                                  Premium
                                </span>
                              )}
                            </div>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                            {template.description}
                          </p>
                          <div className="flex flex-wrap gap-1">
                            {template.tags.slice(0, 3).map((tag) => (
                              <span
                                key={tag}
                                className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded"
                              >
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Step 3: Project Details */}
          {step === 'details' && (
            <form onSubmit={handleSubmit} className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Project Details
                </h3>
                <button
                  type="button"
                  onClick={() => setStep(selectedTemplate ? 'template' : 'method')}
                  className="text-blue-600 hover:text-blue-700 text-sm"
                >
                  ← Back
                </button>
              </div>

              {selectedTemplate && (
                <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center text-white">
                      {selectedTemplate.framework.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <h4 className="font-medium text-blue-900 dark:text-blue-100">
                        {selectedTemplate.name}
                      </h4>
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        {selectedTemplate.description}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Project Name *
                  </label>
                  <input
                    type="text"
                    value={projectData.name}
                    onChange={(e) => setProjectData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="My Awesome Project"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Description
                  </label>
                  <textarea
                    value={projectData.description}
                    onChange={(e) => setProjectData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe your project..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                {!selectedTemplate && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Framework
                      </label>
                      <select
                        value={projectData.framework}
                        onChange={(e) => setProjectData(prev => ({ ...prev, framework: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="react">React</option>
                        <option value="vue">Vue.js</option>
                        <option value="angular">Angular</option>
                        <option value="svelte">Svelte</option>
                        <option value="nextjs">Next.js</option>
                        <option value="nuxt">Nuxt.js</option>
                        <option value="vanilla">Vanilla JS</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Language
                      </label>
                      <select
                        value={projectData.language}
                        onChange={(e) => setProjectData(prev => ({ ...prev, language: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="typescript">TypeScript</option>
                        <option value="javascript">JavaScript</option>
                        <option value="python">Python</option>
                        <option value="java">Java</option>
                        <option value="csharp">C#</option>
                        <option value="go">Go</option>
                        <option value="rust">Rust</option>
                      </select>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex items-center justify-end gap-3 mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isLoading || !projectData.name.trim()}
                  className="flex items-center gap-2 px-6 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                >
                  {isLoading ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Plus className="w-4 h-4" />
                  )}
                  Create Project
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreateProjectModal;
