version: '3.8'

services:
  # FastCodr Backend (Next.js API) - Staging
  backend:
    build:
      context: ./apps/fastcodr-next
      dockerfile: Dockerfile
      args:
        - NODE_ENV=staging
    container_name: fastcodr-backend-staging
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=staging
      - PORT=3001
      - HOSTNAME=0.0.0.0
    env_file:
      - ./apps/fastcodr-next/.env.staging
    depends_on:
      - mongodb
      - redis
    restart: unless-stopped
    networks:
      - fastcodr-staging-network
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # FastCodr Frontend (React + Vite) - Staging
  frontend:
    build:
      context: ./apps/fastcodr-client
      dockerfile: Dockerfile
      args:
        - NODE_ENV=staging
    container_name: fastcodr-frontend-staging
    ports:
      - "8080:80"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - fastcodr-staging-network
    volumes:
      - ./nginx/logs:/var/log/nginx
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB Database - Staging
  mongodb:
    image: mongo:6.0
    container_name: fastcodr-mongodb-staging
    ports:
      - "27018:27017"  # Different port for staging
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME:-fastcodr_staging}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD:-staging_password}
      - MONGO_INITDB_DATABASE=fastcodr_staging
    volumes:
      - mongodb_staging_data:/data/db
      - mongodb_staging_config:/data/configdb
      - ./mongodb-init:/docker-entrypoint-initdb.d:ro
      - ./backups:/backups
    restart: unless-stopped
    networks:
      - fastcodr-staging-network
    command: mongod --auth --bind_ip_all
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and sessions - Staging
  redis:
    image: redis:7-alpine
    container_name: fastcodr-redis-staging
    ports:
      - "6380:6379"  # Different port for staging
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-staging_redis_password}
    volumes:
      - redis_staging_data:/data
    restart: unless-stopped
    networks:
      - fastcodr-staging-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Load Balancer - Staging
  nginx:
    image: nginx:alpine
    container_name: fastcodr-nginx-staging
    ports:
      - "8081:80"
      - "8444:443"
    volumes:
      - ./nginx/nginx.staging.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    networks:
      - fastcodr-staging-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus - Staging
  prometheus:
    image: prom/prometheus:latest
    container_name: fastcodr-prometheus-staging
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus.staging.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_staging_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=72h'  # Shorter retention for staging
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - fastcodr-staging-network

  # Grafana for monitoring dashboards - Staging
  grafana:
    image: grafana/grafana:latest
    container_name: fastcodr-grafana-staging
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-staging123}
      - GF_SERVER_ROOT_URL=http://staging.yourdomain.com:3001
    volumes:
      - grafana_staging_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    restart: unless-stopped
    networks:
      - fastcodr-staging-network

volumes:
  mongodb_staging_data:
    driver: local
  mongodb_staging_config:
    driver: local
  redis_staging_data:
    driver: local
  prometheus_staging_data:
    driver: local
  grafana_staging_data:
    driver: local

networks:
  fastcodr-staging-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
