import { NextRequest, NextResponse } from 'next/server';
import { cashfreeService } from '@/lib/services/cashfree';
import { CashfreeWebhookEvent } from '@/lib/models/Subscription';

// POST /api/webhooks/cashfree - Handle Cashfree webhook events
export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('x-webhook-signature') || '';

    // Verify webhook signature
    if (!cashfreeService.verifyWebhookSignature(body, signature)) {
      console.error('Invalid webhook signature');
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 401 }
      );
    }

    const event: CashfreeWebhookEvent = JSON.parse(body);
    console.log('Received Cashfree webhook:', event.eventType);

    // Process the webhook event
    const processedEvent = cashfreeService.processWebhookEvent(event);

    switch (processedEvent.type) {
      case 'subscription.activated':
        await handleSubscriptionActivated(processedEvent);
        break;

      case 'subscription.cancelled':
        await handleSubscriptionCancelled(processedEvent);
        break;

      case 'subscription.expired':
        await handleSubscriptionExpired(processedEvent);
        break;

      case 'payment.success':
        await handlePaymentSuccess(processedEvent);
        break;

      case 'payment.failed':
        await handlePaymentFailed(processedEvent);
        break;

      default:
        console.log('Unhandled webhook event type:', processedEvent.type);
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error processing Cashfree webhook:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

// Handle subscription activation
async function handleSubscriptionActivated(event: any) {
  try {
    const { subscriptionId, data } = event;
    
    console.log(`Activating subscription: ${subscriptionId}`);

    // TODO: Update subscription in database
    // await updateSubscriptionInDatabase(subscriptionId, {
    //   status: 'active',
    //   currentPeriodStart: new Date(data.currentPeriodStart),
    //   currentPeriodEnd: new Date(data.nextBillingDate),
    //   updatedAt: new Date()
    // });

    // TODO: Update user role to SUBSCRIBER
    // const subscription = await getSubscriptionById(subscriptionId);
    // if (subscription) {
    //   await updateUserRole(subscription.userId, UserRole.SUBSCRIBER);
    // }

    // TODO: Send activation email
    // await sendSubscriptionActivationEmail(subscription.userId);

    console.log(`Subscription ${subscriptionId} activated successfully`);

  } catch (error) {
    console.error('Error handling subscription activation:', error);
    throw error;
  }
}

// Handle subscription cancellation
async function handleSubscriptionCancelled(event: any) {
  try {
    const { subscriptionId, data } = event;
    
    console.log(`Cancelling subscription: ${subscriptionId}`);

    // TODO: Update subscription in database
    // await updateSubscriptionInDatabase(subscriptionId, {
    //   status: 'cancelled',
    //   canceledAt: new Date(),
    //   cancelAtPeriodEnd: true,
    //   updatedAt: new Date()
    // });

    // TODO: Don't immediately downgrade - let them use until period end
    // The subscription should remain active until currentPeriodEnd

    // TODO: Send cancellation confirmation email
    // await sendSubscriptionCancellationEmail(subscription.userId);

    console.log(`Subscription ${subscriptionId} cancelled successfully`);

  } catch (error) {
    console.error('Error handling subscription cancellation:', error);
    throw error;
  }
}

// Handle subscription expiration
async function handleSubscriptionExpired(event: any) {
  try {
    const { subscriptionId, data } = event;
    
    console.log(`Expiring subscription: ${subscriptionId}`);

    // TODO: Update subscription in database
    // await updateSubscriptionInDatabase(subscriptionId, {
    //   status: 'expired',
    //   updatedAt: new Date()
    // });

    // TODO: Downgrade user to free plan
    // const subscription = await getSubscriptionById(subscriptionId);
    // if (subscription) {
    //   await updateUserRole(subscription.userId, UserRole.USER);
    // }

    // TODO: Send expiration notification email
    // await sendSubscriptionExpirationEmail(subscription.userId);

    console.log(`Subscription ${subscriptionId} expired successfully`);

  } catch (error) {
    console.error('Error handling subscription expiration:', error);
    throw error;
  }
}

// Handle successful payment
async function handlePaymentSuccess(event: any) {
  try {
    const { subscriptionId, data } = event;
    const { payment } = data;
    
    console.log(`Payment successful for subscription: ${subscriptionId}`);

    // TODO: Create invoice record
    // await createInvoice({
    //   subscriptionId,
    //   amount: payment.amount,
    //   currency: payment.currency,
    //   status: 'paid',
    //   paymentId: payment.paymentId,
    //   paymentMethod: payment.paymentMethod,
    //   paidAt: new Date()
    // });

    // TODO: Reset usage counters for new billing period
    // await resetSubscriptionUsage(subscriptionId);

    // TODO: Send payment confirmation email
    // await sendPaymentConfirmationEmail(subscription.userId, payment);

    console.log(`Payment processed successfully for subscription ${subscriptionId}`);

  } catch (error) {
    console.error('Error handling payment success:', error);
    throw error;
  }
}

// Handle failed payment
async function handlePaymentFailed(event: any) {
  try {
    const { subscriptionId, data } = event;
    const { payment } = data;
    
    console.log(`Payment failed for subscription: ${subscriptionId}`);

    // TODO: Update subscription status
    // await updateSubscriptionInDatabase(subscriptionId, {
    //   status: 'past_due',
    //   updatedAt: new Date()
    // });

    // TODO: Create failed invoice record
    // await createInvoice({
    //   subscriptionId,
    //   amount: payment.amount,
    //   currency: payment.currency,
    //   status: 'uncollectible',
    //   paymentId: payment.paymentId,
    //   failureReason: payment.failureReason
    // });

    // TODO: Send payment failure notification
    // await sendPaymentFailureEmail(subscription.userId, payment);

    // TODO: Implement retry logic or grace period
    // await schedulePaymentRetry(subscriptionId);

    console.log(`Payment failure handled for subscription ${subscriptionId}`);

  } catch (error) {
    console.error('Error handling payment failure:', error);
    throw error;
  }
}

// Utility function to validate webhook timestamp (prevent replay attacks)
function isValidTimestamp(timestamp: string, toleranceInSeconds: number = 300): boolean {
  const webhookTime = new Date(timestamp).getTime();
  const currentTime = Date.now();
  const timeDifference = Math.abs(currentTime - webhookTime) / 1000;
  
  return timeDifference <= toleranceInSeconds;
}

// Utility function to log webhook events for debugging
function logWebhookEvent(event: CashfreeWebhookEvent) {
  console.log('Webhook Event Details:', {
    type: event.eventType,
    time: event.eventTime,
    subscriptionId: event.data.subscription?.subscriptionId,
    status: event.data.subscription?.status,
    paymentId: event.data.payment?.paymentId,
    amount: event.data.payment?.amount
  });
}
