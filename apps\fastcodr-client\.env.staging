# FastCodr Frontend Staging Environment Configuration

# Environment
NODE_ENV=staging

# Server Configuration - Backend API URL
APP_BASE_URL=http://staging.yourdomain.com:3001

# Frontend Staging URL
VITE_FRONTEND_URL=http://staging.yourdomain.com:8080

# Cashfree Payment Gateway Configuration (Sandbox for staging)
VITE_CASHFREE_CLIENT_ID=your_staging_cashfree_client_id
VITE_CASHFREE_CLIENT_SECRET=your_staging_cashfree_client_secret
VITE_CASHFREE_BASE_URL=https://sandbox.cashfree.com/pg
VITE_CASHFREE_PLAN_ID=fastcodr_premium_monthly_staging
VITE_CASHFREE_ENV=sandbox

# JWT Secret (Staging)
JWT_SECRET=your_staging_jwt_secret_here

# Analytics and Monitoring (Staging)
VITE_ANALYTICS_ID=your_staging_analytics_id
VITE_SENTRY_DSN=your_staging_sentry_dsn

# Feature Flags (Staging)
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PREMIUM_FEATURES=true
VITE_ENABLE_DEBUG_MODE=true
VITE_ENABLE_CONSOLE_LOGS=true

# API Configuration
VITE_API_TIMEOUT=30000
VITE_API_RETRY_COUNT=3

# Development Features (Enabled for staging)
VITE_ENABLE_DEV_TOOLS=true
VITE_ENABLE_MOCK_DATA=false
VITE_ENABLE_HOT_RELOAD=true

# Subscription Configuration
VITE_SUBSCRIPTION_PLAN_PRICE=10
VITE_SUBSCRIPTION_CURRENCY=USD
VITE_SUBSCRIPTION_TRIAL_DAYS=7

# Social Login (Staging)
VITE_GOOGLE_CLIENT_ID=your_staging_google_client_id
VITE_GITHUB_CLIENT_ID=your_staging_github_client_id

# CDN and Assets
VITE_CDN_URL=https://staging-cdn.yourdomain.com
VITE_ASSETS_URL=https://staging-assets.yourdomain.com

# Performance
VITE_ENABLE_PWA=true
VITE_ENABLE_SERVICE_WORKER=true
VITE_CACHE_DURATION=3600

# Security
VITE_ENABLE_CSP=false
VITE_ENABLE_HTTPS_REDIRECT=false

# Monitoring
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_USER_TRACKING=true

# Experimental Features (Staging)
VITE_ENABLE_EXPERIMENTAL_UI=true
VITE_ENABLE_BETA_FEATURES=true
VITE_ENABLE_A_B_TESTING=true
