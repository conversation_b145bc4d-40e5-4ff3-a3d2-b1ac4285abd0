{
  "name": "fastcodr-client",
  "private": true,
  "version": "1.0.0",
  "description": "fastcodr Client Application",
  "author": {
    "name": "Your Name",
    "email": "<EMAIL>"
  },
  "scripts": {
    "dev": "vite",
    "build": "cross-env ELECTRON=true vite build && electron-builder --mac --config electron-builder.json",
    "tsc": "tsc",
    "electron:dev": "vite",
    "builds": "vite build",
    "postinstall": "electron-rebuild -f -w node-pty --arch=x64 && vite build && electron-builder install-app-deps"
  },
  "main": "dist-electron/main.js",
  "dependencies": {
    "@babel/parser": "^7.26.5",
    "@babel/traverse": "^7.26.5",
    "@codemirror/autocomplete": "^6.18.4",
    "@codemirror/commands": "^6.8.0",
    "@codemirror/lang-css": "^6.3.1",
    "@codemirror/lang-html": "^6.4.9",
    "@codemirror/lang-javascript": "^6.2.2",
    "@codemirror/lang-json": "^6.0.1",
    "@codemirror/lang-markdown": "^6.3.2",
    "@codemirror/lang-python": "^6.1.6",
    "@codemirror/language": "^6.10.8",
    "@codemirror/search": "^6.5.8",
    "@codemirror/state": "^6.5.1",
    "@codemirror/view": "^6.36.2",
    "@electron/remote": "^2.1.2",
    "@imgcook/dsl-helper": "^0.0.1",
    "@lezer/highlight": "^1.2.1",
    "@modelcontextprotocol/sdk": "^1.7.0",
    "@mozilla/readability": "^0.5.0",
    "@radix-ui/react-tooltip": "^1.1.7",
    "@sketch-hq/sketch-file-format-ts": "^6.5.0",
    "@webcontainer/api": "1.5.1-internal.9",
    "@xterm/addon-fit": "^0.10.0",
    "@xterm/addon-web-links": "^0.11.0",
    "@xterm/xterm": "^5.5.0",
    "adm-zip": "^0.5.16",
    "ag-psd": "^22.0.2",
    "ai": "^4.1.46",
    "antd": "^5.23.0",
    "antd-style": "^3.7.1",
    "class-variance-authority": "^0.7.1",
    "classnames": "^2.5.1",
    "clsx": "^2.1.1",
    "css": "^3.0.0",
    "electron-notarize": "^1.2.2",
    "fetch-socks": "^1.3.2",
    "framer-motion": "^11.18.0",
    "highlight.js": "^11.11.1",
    "i18next": "^24.2.2",
    "ignore": "^7.0.3",
    "jszip": "^3.10.1",
    "lodash": "^4.17.21",
    "lucide-react": "^0.475.0",
    "node-pty": "^0.10.1",
    "npx-scope-finder": "^1.3.0",
    "posthog-js": "^1.223.3",
    "prettier": "^3.4.2",
    "proxy-agent": "^6.5.0",
    "react": "^18.2.0",
    "react-diff-viewer": "^3.1.1",
    "react-dom": "^18.2.0",
    "react-hot-toast": "^2.5.1",
    "react-i18next": "^15.4.0",
    "react-icons": "^5.5.0",
    "react-markdown": "^9.0.1",
    "react-resizable-panels": "^2.1.7",
    "react-toastify": "^11.0.2",
    "remark-gfm": "4.0.1",
    "socks-proxy-agent": "^8.0.5",
    "styled-components": "^6.1.16",
    "tailwind-merge": "^2.6.0",
    "tar": "^7.4.3",
    "undici": "^7.5.0",
    "uuid": "^11.0.3",
    "xterm": "^5.3.0",
    "zod": "^3.24.1",
    "zustand": "^5.0.3"
  },
  "devDependencies": {
    "@electron/rebuild": "^3.6.0",
    "@iconify/json": "^2.2.300",
    "@iconify/tailwind": "^1.2.0",
    "@originjs/vite-plugin-commonjs": "^1.0.3",
    "@types/lodash": "^4.17.13",
    "@types/node": "^22.10.2",
    "@types/react": "^18.2.43",
    "@types/react-dom": "^18.2.17",
    "@types/react-syntax-highlighter": "^15.5.13",
    "@types/uuid": "^10.0.0",
    "@vitejs/plugin-react": " 4.2.1",
    "autoprefixer": "^10.4.20",
    "babel-loader": "^9.2.1",
    "concurrently": "^8.2.2",
    "cross-env": "^7.0.3",
    "css-loader": "^7.1.2",
    "electron": "29.4.6",
    "electron-builder": "^24.9.1",
    "less": "^4.2.0",
    "postcss": "^8.5.3",
    "sass-embedded": "^1.83.0",
    "style-loader": "^4.0.0",
    "tailwindcss": "^3.4.17",
    "tailwindcss-animate": "^1.0.7",
    "typescript": "^5.5.2",
    "vite": "^5.0.8",
    "vite-plugin-dynamic-import": "^1.6.0",
    "vite-plugin-electron": "^0.15.5",
    "vite-plugin-glsl": "^1.3.1"
  },
  "build": {
    "appId": "com.fastcodr.app",
    "productName": "fastcodr", // formerly We0/WeDev
    "files": [
      "dist/**/*",
      "dist-electron/**/*"
    ],
    "mac": {
      "target": "dir",
      "arch": [
        "arm64"
      ]
    }
  }
}
