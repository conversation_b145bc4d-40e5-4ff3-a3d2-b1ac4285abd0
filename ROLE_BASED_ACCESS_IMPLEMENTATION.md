# 🔐 Role-Based Access Control Implementation

## Overview

This implementation provides a comprehensive role-based access control (RBAC) system for FastCodr with three user roles: **User**, **Subscriber**, and **Admin**. The system includes both frontend and backend protection mechanisms.

## 🎯 Features Implemented

### ✅ **1. Role System**
- **User (Free)**: Basic access to code editor and limited project saves
- **Subscriber (Premium)**: AI assistant, unlimited projects, premium templates
- **Admin**: Full access including user management and analytics

### ✅ **2. Permission System**
- Granular permissions for specific features
- Role-based permission inheritance
- Usage limits per role (projects, AI calls, storage)

### ✅ **3. Frontend Protection**
- `ProtectedRoute` component for route-level protection
- `PermissionGuard` component for feature-level protection
- `RoleBadge` component for displaying user roles
- Higher-order components and hooks for easy integration

### ✅ **4. Backend Protection**
- JWT-based authentication with role information
- Middleware for protecting API routes
- Usage limit enforcement
- Role hierarchy validation

## 📁 File Structure

```
apps/fastcodr-client/src/
├── types/
│   └── auth.ts                    # Role and permission definitions
├── stores/
│   └── userSlice.ts              # Updated user store with role methods
├── components/
│   ├── auth/
│   │   ├── ProtectedRoute.tsx    # Route protection component
│   │   ├── PermissionGuard.tsx   # Feature protection component
│   │   └── RoleBadge.tsx         # Role display component
│   └── examples/
│       └── RoleBasedExample.tsx  # Usage examples

apps/fastcodr-next/
├── lib/
│   └── auth.ts                   # Backend auth utilities and middleware
└── app/api/
    ├── admin/users/route.ts      # Admin-only user management API
    └── chat/route.ts             # Protected AI assistant API
```

## 🚀 Usage Examples

### Frontend Component Protection

```tsx
import PermissionGuard from '@/components/auth/PermissionGuard';
import { Permission, UserRole } from '@/types/auth';

// Protect a feature for subscribers only
<PermissionGuard requiredPermissions={[Permission.AI_ASSISTANT]}>
  <AIAssistantPanel />
</PermissionGuard>

// Protect a route for admins only
<ProtectedRoute requiredRole={UserRole.ADMIN}>
  <AdminDashboard />
</ProtectedRoute>

// Use permission hooks
const { hasPermission, isSubscriber } = usePermissions();

if (hasPermission(Permission.AI_ASSISTANT)) {
  // Show AI features
}
```

### Backend API Protection

```tsx
import { requireAuth, UserRole, Permission } from '@/lib/auth';

export async function POST(request: NextRequest) {
  // Protect API for subscribers with AI assistant permission
  const authResult = await requireAuth(UserRole.SUBSCRIBER, [Permission.AI_ASSISTANT])(request);
  
  if (authResult instanceof Response) {
    return authResult; // Return error response
  }

  const { user } = authResult;
  // Continue with protected logic
}
```

### User Store Integration

```tsx
import useUserStore from '@/stores/userSlice';

const { 
  hasPermission, 
  canAccessRoute, 
  isAdmin, 
  isSubscriber,
  getRemainingUsage 
} = useUserStore();

// Check permissions
if (hasPermission(Permission.AI_ASSISTANT)) {
  // Enable AI features
}

// Check usage limits
const usage = getRemainingUsage();
if (usage.aiCalls > 0 || usage.aiCalls === -1) {
  // Allow AI call
}
```

## 🔧 Configuration

### Role Permissions

```typescript
export const ROLE_PERMISSIONS: RolePermissions = {
  [UserRole.USER]: [
    Permission.VIEW_EDITOR,
    Permission.SAVE_PROJECT
  ],
  [UserRole.SUBSCRIBER]: [
    Permission.VIEW_EDITOR,
    Permission.SAVE_PROJECT,
    Permission.AI_ASSISTANT,
    Permission.UNLIMITED_PROJECTS,
    Permission.PREMIUM_TEMPLATES,
    Permission.EXPORT_PROJECTS
  ],
  [UserRole.ADMIN]: [
    // All subscriber permissions plus:
    Permission.ADMIN_DASHBOARD,
    Permission.MANAGE_USERS,
    Permission.VIEW_ANALYTICS,
    Permission.MANAGE_SUBSCRIPTIONS
  ]
};
```

### Usage Limits

```typescript
export const ROLE_LIMITS: Record<UserRole, UsageLimits> = {
  [UserRole.USER]: {
    projectsPerMonth: 3,
    aiCallsPerMonth: 50,
    storageLimit: 100, // MB
    collaborators: 0
  },
  [UserRole.SUBSCRIBER]: {
    projectsPerMonth: 50,
    aiCallsPerMonth: 1000,
    storageLimit: 1000, // MB
    collaborators: 5
  },
  [UserRole.ADMIN]: {
    projectsPerMonth: -1, // unlimited
    aiCallsPerMonth: -1, // unlimited
    storageLimit: -1, // unlimited
    collaborators: -1 // unlimited
  }
};
```

## 🔒 Security Features

### JWT Token Structure
```typescript
interface JWTPayload {
  userId: string;
  email: string;
  role: UserRole;
  permissions: Permission[];
  iat: number;
  exp: number;
}
```

### Token Verification
- Automatic token validation on protected routes
- Role hierarchy checking
- Permission-based access control
- Usage limit enforcement

### Frontend Security
- Route-level protection with redirects
- Component-level feature hiding
- Graceful upgrade prompts for premium features
- Automatic logout on token expiration

## 🎨 UI Components

### Role Badge
```tsx
<RoleBadge role={UserRole.SUBSCRIBER} size="lg" showIcon={true} />
```

### Permission Guard with Fallback
```tsx
<PermissionGuard 
  requiredPermissions={[Permission.AI_ASSISTANT]}
  fallback={<UpgradePrompt />}
  showUpgradePrompt={true}
>
  <PremiumFeature />
</PermissionGuard>
```

### Inline Permission Check
```tsx
<PermissionGuard 
  requiredRole={UserRole.ADMIN}
  inline={true}
  fallback={<span>Admin Only</span>}
>
  <AdminButton />
</PermissionGuard>
```

## 📊 Integration Points

### Subscription System
- Automatic role upgrade on subscription activation
- Role downgrade on subscription cancellation
- Usage limit updates based on subscription tier

### User Management
- Admin interface for role management
- User activity tracking
- Usage analytics and reporting

### API Protection
- All AI-related endpoints protected
- Admin endpoints restricted to admin role
- Usage tracking and limit enforcement

## 🚀 Next Steps

1. **Database Integration**: Connect to actual user database
2. **Usage Tracking**: Implement real-time usage monitoring
3. **Subscription Integration**: Connect with Cashfree subscription status
4. **Admin Dashboard**: Build comprehensive admin interface
5. **Analytics**: Add detailed usage analytics and reporting

## 🔧 Testing

The implementation includes a comprehensive example component (`RoleBasedExample.tsx`) that demonstrates:
- Role-based feature visibility
- Permission checking
- Usage limit display
- Upgrade prompts
- Interactive role testing

## 📝 Notes

- All components are TypeScript-enabled with proper type safety
- Responsive design with dark mode support
- Graceful degradation for unauthorized access
- Comprehensive error handling and user feedback
- Modular and reusable component architecture

This role-based access system provides a solid foundation for implementing tiered features and premium subscriptions in FastCodr while maintaining security and user experience.
