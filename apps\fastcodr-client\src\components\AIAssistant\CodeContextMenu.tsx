import React, { useState, useEffect } from 'react';
import { Bo<PERSON>, Code, FileText, Lightbulb, Bug, Zap } from 'lucide-react';
import PermissionGuard from '@/components/auth/PermissionGuard';
import { Permission } from '@/types/auth';
import useUserStore from '@/stores/userSlice';
import { useChat } from 'ai/react';
import { toast } from 'react-hot-toast';

interface CodeContextMenuProps {
  isVisible: boolean;
  position: { x: number; y: number };
  selectedCode: string;
  onClose: () => void;
  onInsertCode?: (code: string) => void;
}

const CodeContextMenu: React.FC<CodeContextMenuProps> = ({
  isVisible,
  position,
  selectedCode,
  onClose,
  onInsertCode
}) => {
  const { token, getRemainingUsage } = useUserStore();
  const [activeAction, setActiveAction] = useState<string | null>(null);
  const [response, setResponse] = useState<string>('');
  const remainingUsage = getRemainingUsage();

  const { append, isLoading, messages } = useChat({
    api: `${process.env.APP_BASE_URL}/api/chat`,
    headers: {
      ...(token && { Authorization: `Bearer ${token}` }),
    },
    body: {
      model: 'claude-3-5-sonnet',
      mode: 'chat',
      otherConfig: {
        extra: {
          isBackEnd: false,
          backendLanguage: 'javascript'
        }
      }
    },
    onError: (error) => {
      console.error('AI Context Menu error:', error);
      toast.error('AI Assistant error: ' + error.message);
      setActiveAction(null);
    },
    onFinish: (message) => {
      setResponse(message.content);
    }
  });

  useEffect(() => {
    if (!isVisible) {
      setActiveAction(null);
      setResponse('');
    }
  }, [isVisible]);

  const handleAction = async (action: string, prompt: string) => {
    if (remainingUsage.aiCalls === 0) {
      toast.error('AI call limit reached. Upgrade to Premium for unlimited access!');
      return;
    }

    setActiveAction(action);
    setResponse('');
    
    const fullPrompt = `${prompt}\n\nCode:\n\`\`\`\n${selectedCode}\n\`\`\``;
    
    await append({
      role: 'user',
      content: fullPrompt
    });
  };

  const actions = [
    {
      id: 'explain',
      label: 'Explain Code',
      icon: <FileText className="w-4 h-4" />,
      prompt: 'Please explain what this code does, how it works, and any important details:'
    },
    {
      id: 'improve',
      label: 'Suggest Improvements',
      icon: <Lightbulb className="w-4 h-4" />,
      prompt: 'Please analyze this code and suggest improvements for better performance, readability, or best practices:'
    },
    {
      id: 'debug',
      label: 'Find Bugs',
      icon: <Bug className="w-4 h-4" />,
      prompt: 'Please analyze this code for potential bugs, errors, or issues and suggest fixes:'
    },
    {
      id: 'optimize',
      label: 'Optimize',
      icon: <Zap className="w-4 h-4" />,
      prompt: 'Please optimize this code for better performance and efficiency:'
    },
    {
      id: 'refactor',
      label: 'Refactor',
      icon: <Code className="w-4 h-4" />,
      prompt: 'Please refactor this code to make it cleaner, more maintainable, and follow best practices:'
    }
  ];

  if (!isVisible) return null;

  return (
    <PermissionGuard requiredPermissions={[Permission.AI_ASSISTANT]}>
      <div
        className="fixed z-50 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 min-w-[300px] max-w-[500px]"
        style={{
          left: position.x,
          top: position.y,
          transform: 'translate(-50%, -100%)'
        }}
      >
        {/* Header */}
        <div className="flex items-center gap-2 p-3 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-t-lg">
          <Bot className="w-4 h-4" />
          <span className="font-medium text-sm">AI Code Assistant</span>
          {remainingUsage.aiCalls !== -1 && (
            <span className="ml-auto text-xs bg-white/20 px-2 py-1 rounded-full">
              {remainingUsage.aiCalls} calls left
            </span>
          )}
        </div>

        {/* Actions */}
        {!activeAction && (
          <div className="p-2">
            <div className="text-xs text-gray-500 dark:text-gray-400 mb-2 px-2">
              Selected: {selectedCode.length > 50 ? selectedCode.substring(0, 50) + '...' : selectedCode}
            </div>
            
            <div className="space-y-1">
              {actions.map((action) => (
                <button
                  key={action.id}
                  onClick={() => handleAction(action.id, action.prompt)}
                  disabled={remainingUsage.aiCalls === 0}
                  className="w-full flex items-center gap-3 p-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {action.icon}
                  <span className="text-sm">{action.label}</span>
                </button>
              ))}
            </div>
            
            <div className="border-t border-gray-200 dark:border-gray-700 mt-2 pt-2">
              <button
                onClick={onClose}
                className="w-full text-center text-xs text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 py-1"
              >
                Close
              </button>
            </div>
          </div>
        )}

        {/* Loading/Response */}
        {activeAction && (
          <div className="p-4 max-h-[400px] overflow-y-auto">
            {isLoading ? (
              <div className="flex items-center gap-2 text-gray-500">
                <Bot className="w-4 h-4" />
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-sm">Analyzing code...</span>
              </div>
            ) : response ? (
              <div className="space-y-3">
                <div className="text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                  {response}
                </div>
                
                <div className="flex gap-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                  {onInsertCode && response.includes('```') && (
                    <button
                      onClick={() => {
                        // Extract code from response
                        const codeMatch = response.match(/```[\w]*\n([\s\S]*?)```/);
                        if (codeMatch) {
                          onInsertCode(codeMatch[1]);
                          toast.success('Code inserted!');
                        }
                      }}
                      className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-xs rounded transition-colors"
                    >
                      Insert Code
                    </button>
                  )}
                  
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(response);
                      toast.success('Response copied to clipboard!');
                    }}
                    className="px-3 py-1 bg-gray-500 hover:bg-gray-600 text-white text-xs rounded transition-colors"
                  >
                    Copy
                  </button>
                  
                  <button
                    onClick={onClose}
                    className="px-3 py-1 bg-gray-300 hover:bg-gray-400 text-gray-700 text-xs rounded transition-colors ml-auto"
                  >
                    Close
                  </button>
                </div>
              </div>
            ) : null}
          </div>
        )}
      </div>
    </PermissionGuard>
  );
};

export default CodeContextMenu;
