import React, { useState } from 'react';
import { 
  MoreVertical, 
  Edit, 
  Trash2, 
  Share, 
  Download, 
  Eye, 
  GitFork, 
  Star, 
  Clock,
  Calendar,
  FileText,
  Globe,
  Lock
} from 'lucide-react';
import { Project } from '@/stores/projectSlice';
import { formatDistanceToNow } from 'date-fns';
import { toast } from 'react-hot-toast';

interface ProjectCardProps {
  project: Project;
  viewMode: 'grid' | 'list';
  onOpen: () => void;
  onDelete: () => void;
  getFrameworkIcon: (framework: string) => string;
  getProjectStatusColor: (status: Project['status']) => string;
}

const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  viewMode,
  onOpen,
  onDelete,
  getFrameworkIcon,
  getProjectStatusColor
}) => {
  const [showMenu, setShowMenu] = useState(false);

  const handleShare = () => {
    const shareUrl = `${window.location.origin}/project/${project.id}`;
    navigator.clipboard.writeText(shareUrl);
    toast.success('Project link copied to clipboard');
    setShowMenu(false);
  };

  const handleExport = () => {
    // TODO: Implement project export
    toast.info('Export feature coming soon');
    setShowMenu(false);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  if (viewMode === 'list') {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
        <div className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4 flex-1 min-w-0">
              {/* Project Icon and Info */}
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center text-white text-xl">
                  {getFrameworkIcon(project.metadata.framework)}
                </div>
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h3 
                    className="text-lg font-semibold text-gray-900 dark:text-white truncate cursor-pointer hover:text-blue-600"
                    onClick={onOpen}
                  >
                    {project.name}
                  </h3>
                  
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getProjectStatusColor(project.status)}`}>
                    {project.status}
                  </span>
                  
                  {project.isPublic ? (
                    <Globe className="w-4 h-4 text-green-500" title="Public project" />
                  ) : (
                    <Lock className="w-4 h-4 text-gray-400" title="Private project" />
                  )}
                </div>
                
                <p className="text-gray-600 dark:text-gray-400 text-sm truncate mb-2">
                  {project.description || 'No description'}
                </p>
                
                <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                  <span className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    Updated {formatDistanceToNow(new Date(project.updatedAt), { addSuffix: true })}
                  </span>
                  
                  <span className="flex items-center gap-1">
                    <FileText className="w-3 h-3" />
                    {project.fileCount} files
                  </span>
                  
                  <span className="flex items-center gap-1">
                    <Eye className="w-3 h-3" />
                    {project.viewCount} views
                  </span>
                  
                  <span>{formatFileSize(project.totalSize)}</span>
                </div>
              </div>
            </div>

            {/* Tags and Actions */}
            <div className="flex items-center gap-4">
              <div className="flex flex-wrap gap-1">
                {project.tags.slice(0, 3).map((tag) => (
                  <span
                    key={tag}
                    className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded"
                  >
                    {tag}
                  </span>
                ))}
                {project.tags.length > 3 && (
                  <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-500 text-xs rounded">
                    +{project.tags.length - 3}
                  </span>
                )}
              </div>
              
              <div className="relative">
                <button
                  onClick={() => setShowMenu(!showMenu)}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <MoreVertical className="w-4 h-4" />
                </button>
                
                {showMenu && (
                  <ProjectMenu
                    onShare={handleShare}
                    onExport={handleExport}
                    onDelete={onDelete}
                    onClose={() => setShowMenu(false)}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Grid view
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200 group">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center text-white text-lg">
              {getFrameworkIcon(project.metadata.framework)}
            </div>
            
            <div>
              <h3 
                className="font-semibold text-gray-900 dark:text-white cursor-pointer hover:text-blue-600 transition-colors"
                onClick={onOpen}
              >
                {project.name}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {project.metadata.framework} • {project.metadata.language}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getProjectStatusColor(project.status)}`}>
              {project.status}
            </span>
            
            {project.isPublic ? (
              <Globe className="w-4 h-4 text-green-500" title="Public project" />
            ) : (
              <Lock className="w-4 h-4 text-gray-400" title="Private project" />
            )}
            
            <div className="relative">
              <button
                onClick={() => setShowMenu(!showMenu)}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <MoreVertical className="w-4 h-4" />
              </button>
              
              {showMenu && (
                <ProjectMenu
                  onShare={handleShare}
                  onExport={handleExport}
                  onDelete={onDelete}
                  onClose={() => setShowMenu(false)}
                />
              )}
            </div>
          </div>
        </div>

        {/* Description */}
        <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
          {project.description || 'No description provided'}
        </p>

        {/* Tags */}
        <div className="flex flex-wrap gap-1 mb-4">
          {project.tags.slice(0, 3).map((tag) => (
            <span
              key={tag}
              className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded"
            >
              {tag}
            </span>
          ))}
          {project.tags.length > 3 && (
            <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-500 text-xs rounded">
              +{project.tags.length - 3}
            </span>
          )}
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-4">
          <div className="flex items-center gap-3">
            <span className="flex items-center gap-1">
              <Eye className="w-3 h-3" />
              {project.viewCount}
            </span>
            
            <span className="flex items-center gap-1">
              <GitFork className="w-3 h-3" />
              {project.forkCount}
            </span>
            
            <span className="flex items-center gap-1">
              <Star className="w-3 h-3" />
              {project.starCount}
            </span>
          </div>
          
          <span>{formatFileSize(project.totalSize)}</span>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span className="flex items-center gap-1">
            <Clock className="w-3 h-3" />
            {formatDistanceToNow(new Date(project.updatedAt), { addSuffix: true })}
          </span>
          
          <span>{project.fileCount} files</span>
        </div>
      </div>
    </div>
  );
};

interface ProjectMenuProps {
  onShare: () => void;
  onExport: () => void;
  onDelete: () => void;
  onClose: () => void;
}

const ProjectMenu: React.FC<ProjectMenuProps> = ({ onShare, onExport, onDelete, onClose }) => {
  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 z-10" onClick={onClose} />
      
      {/* Menu */}
      <div className="absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-20">
        <div className="py-1">
          <button
            onClick={onShare}
            className="w-full flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <Share className="w-4 h-4" />
            Share Project
          </button>
          
          <button
            onClick={onExport}
            className="w-full flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <Download className="w-4 h-4" />
            Export Project
          </button>
          
          <hr className="my-1 border-gray-200 dark:border-gray-700" />
          
          <button
            onClick={onDelete}
            className="w-full flex items-center gap-2 px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
          >
            <Trash2 className="w-4 h-4" />
            Delete Project
          </button>
        </div>
      </div>
    </>
  );
};

export default ProjectCard;
