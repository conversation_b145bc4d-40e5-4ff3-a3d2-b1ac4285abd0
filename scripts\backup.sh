#!/bin/bash

# FastCodr Backup Script
# This script creates backups of the database and application files

set -e

# Configuration
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging
LOG_FILE="$BACKUP_DIR/backup_$DATE.log"
mkdir -p "$BACKUP_DIR"

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Function to backup MongoDB
backup_mongodb() {
    log "Starting MongoDB backup..."
    
    MONGO_BACKUP_DIR="$BACKUP_DIR/mongodb_$DATE"
    mkdir -p "$MONGO_BACKUP_DIR"
    
    # Check if MongoDB container is running
    if ! docker-compose ps mongodb | grep -q "Up"; then
        log_error "MongoDB container is not running"
        return 1
    fi
    
    # Create MongoDB dump
    docker-compose exec -T mongodb mongodump \
        --out /backups/mongodb_$DATE \
        --gzip \
        --verbose 2>&1 | tee -a "$LOG_FILE"
    
    if [ $? -eq 0 ]; then
        log_success "MongoDB backup completed: $MONGO_BACKUP_DIR"
        
        # Compress the backup
        tar -czf "$BACKUP_DIR/mongodb_backup_$DATE.tar.gz" -C "$BACKUP_DIR" "mongodb_$DATE"
        rm -rf "$MONGO_BACKUP_DIR"
        log_success "MongoDB backup compressed: mongodb_backup_$DATE.tar.gz"
    else
        log_error "MongoDB backup failed"
        return 1
    fi
}

# Function to backup application files
backup_application() {
    log "Starting application files backup..."
    
    APP_BACKUP_FILE="$BACKUP_DIR/application_backup_$DATE.tar.gz"
    
    # Backup application files (excluding node_modules and build artifacts)
    tar -czf "$APP_BACKUP_FILE" \
        --exclude='node_modules' \
        --exclude='dist' \
        --exclude='build' \
        --exclude='.git' \
        --exclude='logs' \
        --exclude='*.log' \
        apps/ \
        docker-compose*.yml \
        nginx/ \
        monitoring/ \
        scripts/ \
        .env* \
        README.md \
        DEPLOYMENT.md 2>&1 | tee -a "$LOG_FILE"
    
    if [ $? -eq 0 ]; then
        log_success "Application backup completed: $APP_BACKUP_FILE"
    else
        log_error "Application backup failed"
        return 1
    fi
}

# Function to backup Redis data
backup_redis() {
    log "Starting Redis backup..."
    
    REDIS_BACKUP_FILE="$BACKUP_DIR/redis_backup_$DATE.rdb"
    
    # Check if Redis container is running
    if ! docker-compose ps redis | grep -q "Up"; then
        log_warning "Redis container is not running, skipping Redis backup"
        return 0
    fi
    
    # Create Redis backup
    docker-compose exec -T redis redis-cli BGSAVE 2>&1 | tee -a "$LOG_FILE"
    sleep 5  # Wait for background save to complete
    
    # Copy the RDB file
    docker cp $(docker-compose ps -q redis):/data/dump.rdb "$REDIS_BACKUP_FILE" 2>&1 | tee -a "$LOG_FILE"
    
    if [ $? -eq 0 ]; then
        log_success "Redis backup completed: $REDIS_BACKUP_FILE"
    else
        log_error "Redis backup failed"
        return 1
    fi
}

# Function to backup SSL certificates
backup_ssl() {
    log "Starting SSL certificates backup..."
    
    if [ -d "nginx/ssl" ]; then
        SSL_BACKUP_FILE="$BACKUP_DIR/ssl_backup_$DATE.tar.gz"
        tar -czf "$SSL_BACKUP_FILE" nginx/ssl/ 2>&1 | tee -a "$LOG_FILE"
        
        if [ $? -eq 0 ]; then
            log_success "SSL certificates backup completed: $SSL_BACKUP_FILE"
        else
            log_error "SSL certificates backup failed"
            return 1
        fi
    else
        log_warning "SSL certificates directory not found, skipping SSL backup"
    fi
}

# Function to cleanup old backups
cleanup_old_backups() {
    log "Cleaning up old backups (older than $RETENTION_DAYS days)..."
    
    find "$BACKUP_DIR" -name "*.tar.gz" -type f -mtime +$RETENTION_DAYS -delete 2>&1 | tee -a "$LOG_FILE"
    find "$BACKUP_DIR" -name "*.rdb" -type f -mtime +$RETENTION_DAYS -delete 2>&1 | tee -a "$LOG_FILE"
    find "$BACKUP_DIR" -name "*.log" -type f -mtime +$RETENTION_DAYS -delete 2>&1 | tee -a "$LOG_FILE"
    
    log_success "Old backups cleaned up"
}

# Function to verify backups
verify_backups() {
    log "Verifying backup integrity..."
    
    # Check if backup files exist and are not empty
    for backup_file in "$BACKUP_DIR"/*_backup_$DATE.*; do
        if [ -f "$backup_file" ] && [ -s "$backup_file" ]; then
            log_success "Backup file verified: $(basename "$backup_file")"
        else
            log_error "Backup file verification failed: $(basename "$backup_file")"
        fi
    done
}

# Function to send backup notification
send_notification() {
    local status=$1
    local message=$2
    
    # You can integrate with your notification system here
    # Examples: Slack, Discord, Email, etc.
    
    log "Backup notification: $status - $message"
    
    # Example: Send to webhook (uncomment and configure)
    # curl -X POST -H 'Content-type: application/json' \
    #     --data "{\"text\":\"FastCodr Backup $status: $message\"}" \
    #     YOUR_WEBHOOK_URL
}

# Main backup function
main() {
    log "=== FastCodr Backup Started ==="
    log "Backup date: $DATE"
    log "Backup directory: $BACKUP_DIR"
    
    local backup_success=true
    
    # Perform backups
    backup_mongodb || backup_success=false
    backup_application || backup_success=false
    backup_redis || backup_success=false
    backup_ssl || backup_success=false
    
    # Verify backups
    verify_backups
    
    # Cleanup old backups
    cleanup_old_backups
    
    # Calculate backup size
    BACKUP_SIZE=$(du -sh "$BACKUP_DIR"/*_$DATE.* 2>/dev/null | awk '{total+=$1} END {print total}' || echo "0")
    
    if [ "$backup_success" = true ]; then
        log_success "=== FastCodr Backup Completed Successfully ==="
        log_success "Total backup size: $BACKUP_SIZE"
        send_notification "SUCCESS" "Backup completed successfully. Size: $BACKUP_SIZE"
    else
        log_error "=== FastCodr Backup Completed with Errors ==="
        send_notification "ERROR" "Backup completed with errors. Check logs: $LOG_FILE"
        exit 1
    fi
}

# Check if running as root or with sudo
if [ "$EUID" -eq 0 ]; then
    log_warning "Running as root. Consider running with appropriate user permissions."
fi

# Run main function
main "$@"
