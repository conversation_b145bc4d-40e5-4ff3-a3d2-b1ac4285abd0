#!/bin/bash

# FastCodr Deployment Script
set -e

echo "🚀 Starting FastCodr deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Environment setup
print_status "Setting up environment..."

# Check if production environment files exist
if [ ! -f "apps/fastcodr-next/.env.production" ]; then
    print_warning "Backend production environment file not found. Creating from template..."
    cp apps/fastcodr-next/.env.example apps/fastcodr-next/.env.production
fi

if [ ! -f "apps/fastcodr-client/.env.production" ]; then
    print_warning "Frontend production environment file not found. Creating from template..."
    cp apps/fastcodr-client/.env.example apps/fastcodr-client/.env.production
fi

# Build and deploy
print_status "Building Docker images..."
docker-compose build

print_status "Starting services..."
docker-compose up -d

# Wait for services to be ready
print_status "Waiting for services to start..."
sleep 30

# Health checks
print_status "Performing health checks..."

# Check backend health
if curl -f http://localhost:3001 > /dev/null 2>&1; then
    print_success "Backend is running on http://localhost:3001"
else
    print_error "Backend health check failed"
fi

# Check frontend health
if curl -f http://localhost:80 > /dev/null 2>&1; then
    print_success "Frontend is running on http://localhost:80"
else
    print_error "Frontend health check failed"
fi

# Check database health
if docker-compose exec -T mongodb mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1; then
    print_success "MongoDB is running"
else
    print_error "MongoDB health check failed"
fi

print_success "🎉 FastCodr deployment completed!"
print_status "Access your application at:"
print_status "  Frontend: http://localhost"
print_status "  Backend API: http://localhost:3001"
print_status "  MongoDB: localhost:27017"

print_status "To view logs, run: docker-compose logs -f"
print_status "To stop services, run: docker-compose down"
