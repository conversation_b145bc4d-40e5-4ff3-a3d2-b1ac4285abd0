import { create } from "zustand";
import { persist } from "zustand/middleware";
import { 
  getSubscriptionStatus, 
  setSubscriptionStatus, 
  getSubscriptionId,
  clearSubscriptionData,
  verifySubscription 
} from "@/lib/cashfree";

export interface SubscriptionState {
  isSubscribed: boolean;
  subscriptionId: string | null;
  subscriptionData: any | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setSubscription: (isSubscribed: boolean, subscriptionId?: string) => void;
  clearSubscription: () => void;
  verifySubscriptionStatus: () => Promise<void>;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  checkLocalSubscription: () => void;
}

const useSubscriptionStore = create<SubscriptionState>()(
  persist(
    (set, get) => ({
      isSubscribed: false,
      subscriptionId: null,
      subscriptionData: null,
      isLoading: false,
      error: null,

      setSubscription: (isSubscribed: boolean, subscriptionId?: string) => {
        setSubscriptionStatus(isSubscribed, subscriptionId);
        set({
          isSubscribed,
          subscriptionId: subscriptionId || null,
          error: null,
        });
      },

      clearSubscription: () => {
        clearSubscriptionData();
        set({
          isSubscribed: false,
          subscriptionId: null,
          subscriptionData: null,
          error: null,
        });
      },

      verifySubscriptionStatus: async () => {
        const { subscriptionId } = get();
        if (!subscriptionId) {
          set({ isSubscribed: false });
          return;
        }

        set({ isLoading: true, error: null });
        
        try {
          const result = await verifySubscription(subscriptionId);
          
          if (result.error) {
            throw new Error(result.error);
          }

          const isActive = result.subscription_status === 'ACTIVE';
          
          set({
            isSubscribed: isActive,
            subscriptionData: result,
            isLoading: false,
          });

          // Update local storage
          setSubscriptionStatus(isActive, subscriptionId);
          
        } catch (error: any) {
          console.error('Subscription verification failed:', error);
          set({
            isSubscribed: false,
            error: error.message,
            isLoading: false,
          });
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      checkLocalSubscription: () => {
        const isSubscribed = getSubscriptionStatus();
        const subscriptionId = getSubscriptionId();
        
        set({
          isSubscribed,
          subscriptionId,
        });
      },
    }),
    {
      name: "subscription-storage",
      partialize: (state) => ({
        isSubscribed: state.isSubscribed,
        subscriptionId: state.subscriptionId,
        subscriptionData: state.subscriptionData,
      }),
      version: 1,
      onRehydrateStorage: () => (state) => {
        // Check local storage on app start
        state?.checkLocalSubscription();
      },
    }
  )
);

export default useSubscriptionStore;
