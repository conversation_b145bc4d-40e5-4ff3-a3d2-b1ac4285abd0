import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { toast } from 'react-hot-toast';

// Subscription interfaces
export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'monthly' | 'yearly';
  features: string[];
  limits: {
    projects: number;
    aiCalls: number;
    storage: number;
    collaborators: number;
    templates: number;
    exportFormats: string[];
  };
  isPopular: boolean;
  isActive: boolean;
  trialDays?: number;
}

export interface Subscription {
  id: string;
  userId: string;
  planId: string;
  status: 'active' | 'cancelled' | 'expired' | 'past_due' | 'trialing' | 'incomplete';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  canceledAt?: Date;
  trialStart?: Date;
  trialEnd?: Date;
  provider: 'cashfree' | 'stripe' | 'razorpay';
  providerSubscriptionId: string;
  amount: number;
  currency: string;
  usage: {
    projects: number;
    aiCalls: number;
    storage: number;
    resetDate: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface UsageInfo {
  projects: { used: number; limit: number; percentage: number };
  aiCalls: { used: number; limit: number; percentage: number };
  storage: { used: number; limit: number; percentage: number };
}

export interface BillingInfo {
  nextBillingDate: Date;
  amount: string;
  status: string;
  cancelAtPeriodEnd: boolean;
}

interface SubscriptionState {
  // Current subscription data
  currentSubscription: Subscription | null;
  currentPlan: SubscriptionPlan | null;
  availablePlans: SubscriptionPlan[];
  usage: UsageInfo | null;
  billingInfo: BillingInfo | null;

  // Loading states
  isLoading: boolean;
  isProcessing: boolean;

  // Legacy compatibility
  isSubscribed: boolean;
  subscriptionId: string | null;
  subscriptionData: any | null;
  error: string | null;

  // Actions
  loadSubscriptionData: () => Promise<void>;
  loadPlans: () => Promise<void>;
  createSubscription: (planId: string) => Promise<{ paymentUrl?: string; subscriptionId: string }>;
  cancelSubscription: () => Promise<void>;
  reactivateSubscription: () => Promise<void>;
  upgradeSubscription: (planId: string) => Promise<void>;

  // Feature access checks
  hasFeature: (feature: string) => boolean;
  canUseFeature: (feature: string, currentUsage?: number) => boolean;
  getRemainingUsage: () => { projects: number; aiCalls: number; storage: number };

  // Usage tracking
  incrementUsage: (type: 'projects' | 'aiCalls' | 'storage', amount?: number) => void;
  resetUsage: () => void;

  // Utility functions
  isPremium: () => boolean;
  isTrialing: () => boolean;
  getSubscriptionStatus: () => string;

  // Legacy methods for backward compatibility
  setSubscription: (isSubscribed: boolean, subscriptionId?: string) => void;
  clearSubscription: () => void;
  verifySubscriptionStatus: () => Promise<void>;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  checkLocalSubscription: () => void;
}

const useSubscriptionStore = create<SubscriptionState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentSubscription: null,
      currentPlan: null,
      availablePlans: [],
      usage: null,
      billingInfo: null,
      isLoading: false,
      isProcessing: false,

      // Legacy compatibility
      isSubscribed: false,
      subscriptionId: null,
      subscriptionData: null,
      error: null,

      // Load subscription data
      loadSubscriptionData: async () => {
        set({ isLoading: true });
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/subscriptions`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            throw new Error('Failed to load subscription data');
          }

          const data = await response.json();
          set({
            currentSubscription: data.data.currentSubscription,
            currentPlan: data.data.currentPlan,
            availablePlans: data.data.availablePlans,
            usage: data.data.usage,
            billingInfo: data.data.billingInfo,
            // Update legacy fields
            isSubscribed: !!data.data.currentSubscription && data.data.currentSubscription.status === 'active',
            subscriptionId: data.data.currentSubscription?.id || null
          });
        } catch (error) {
          console.error('Error loading subscription data:', error);
          toast.error('Failed to load subscription data');
          set({ error: error instanceof Error ? error.message : 'Failed to load subscription data' });
        } finally {
          set({ isLoading: false });
        }
      },

      // Load available plans
      loadPlans: async () => {
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/subscriptions/plans`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            throw new Error('Failed to load plans');
          }

          const data = await response.json();
          set({ availablePlans: data.data.plans });
        } catch (error) {
          console.error('Error loading plans:', error);
          toast.error('Failed to load subscription plans');
        }
      },

      // Create new subscription
      createSubscription: async (planId: string) => {
        set({ isProcessing: true });
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/subscriptions`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ planId })
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Failed to create subscription');
          }

          const data = await response.json();

          // Refresh subscription data
          await get().loadSubscriptionData();

          toast.success('Subscription created successfully');
          return {
            paymentUrl: data.data.paymentUrl,
            subscriptionId: data.data.subscriptionId
          };
        } catch (error) {
          console.error('Error creating subscription:', error);
          toast.error(error instanceof Error ? error.message : 'Failed to create subscription');
          throw error;
        } finally {
          set({ isProcessing: false });
        }
      },

      // Cancel subscription
      cancelSubscription: async () => {
        set({ isProcessing: true });
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/subscriptions`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ action: 'cancel' })
          });

          if (!response.ok) {
            throw new Error('Failed to cancel subscription');
          }

          // Refresh subscription data
          await get().loadSubscriptionData();

          toast.success('Subscription cancelled successfully');
        } catch (error) {
          console.error('Error cancelling subscription:', error);
          toast.error('Failed to cancel subscription');
          throw error;
        } finally {
          set({ isProcessing: false });
        }
      },

      // Reactivate subscription
      reactivateSubscription: async () => {
        set({ isProcessing: true });
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/subscriptions`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ action: 'reactivate' })
          });

          if (!response.ok) {
            throw new Error('Failed to reactivate subscription');
          }

          // Refresh subscription data
          await get().loadSubscriptionData();

          toast.success('Subscription reactivated successfully');
        } catch (error) {
          console.error('Error reactivating subscription:', error);
          toast.error('Failed to reactivate subscription');
          throw error;
        } finally {
          set({ isProcessing: false });
        }
      },

      // Upgrade subscription
      upgradeSubscription: async (planId: string) => {
        set({ isProcessing: true });
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/subscriptions`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ action: 'upgrade', planId })
          });

          if (!response.ok) {
            throw new Error('Failed to upgrade subscription');
          }

          // Refresh subscription data
          await get().loadSubscriptionData();

          toast.success('Subscription upgraded successfully');
        } catch (error) {
          console.error('Error upgrading subscription:', error);
          toast.error('Failed to upgrade subscription');
          throw error;
        } finally {
          set({ isProcessing: false });
        }
      },

      // Feature access checks
      hasFeature: (feature: string) => {
        const { currentPlan } = get();
        if (!currentPlan) {
          // Free plan features
          const freeFeatures = ['Basic code editor', 'Project saving', 'Community templates'];
          return freeFeatures.some(f => f.toLowerCase().includes(feature.toLowerCase()));
        }

        return currentPlan.features.some(f => f.toLowerCase().includes(feature.toLowerCase()));
      },

      canUseFeature: (feature: string, currentUsage = 0) => {
        const { currentPlan, usage } = get();

        if (!get().hasFeature(feature)) {
          return false;
        }

        if (!currentPlan || !usage) {
          return false;
        }

        // Check usage limits
        switch (feature.toLowerCase()) {
          case 'projects':
            return currentPlan.limits.projects === -1 || usage.projects.used < currentPlan.limits.projects;
          case 'ai':
          case 'ai assistant':
            return currentPlan.limits.aiCalls === -1 || usage.aiCalls.used < currentPlan.limits.aiCalls;
          case 'storage':
            return currentPlan.limits.storage === -1 || usage.storage.used < currentPlan.limits.storage;
          default:
            return true;
        }
      },

      getRemainingUsage: () => {
        const { currentPlan, usage } = get();

        if (!currentPlan || !usage) {
          return { projects: 3, aiCalls: 0, storage: 100 }; // Free plan limits
        }

        return {
          projects: currentPlan.limits.projects === -1 ? -1 : Math.max(0, currentPlan.limits.projects - usage.projects.used),
          aiCalls: currentPlan.limits.aiCalls === -1 ? -1 : Math.max(0, currentPlan.limits.aiCalls - usage.aiCalls.used),
          storage: currentPlan.limits.storage === -1 ? -1 : Math.max(0, currentPlan.limits.storage - usage.storage.used)
        };
      },

      // Usage tracking
      incrementUsage: (type: 'projects' | 'aiCalls' | 'storage', amount = 1) => {
        const { usage } = get();
        if (!usage) return;

        const newUsage = { ...usage };
        newUsage[type].used += amount;
        newUsage[type].percentage = newUsage[type].limit === -1 ? 0 : (newUsage[type].used / newUsage[type].limit) * 100;

        set({ usage: newUsage });
      },

      resetUsage: () => {
        const { usage } = get();
        if (!usage) return;

        const newUsage = {
          projects: { ...usage.projects, used: 0, percentage: 0 },
          aiCalls: { ...usage.aiCalls, used: 0, percentage: 0 },
          storage: { ...usage.storage, used: 0, percentage: 0 }
        };

        set({ usage: newUsage });
      },

      // Utility functions
      isPremium: () => {
        const { currentPlan, currentSubscription } = get();
        return currentPlan?.id !== 'free' && currentSubscription?.status === 'active';
      },

      isTrialing: () => {
        const { currentSubscription } = get();
        return currentSubscription?.status === 'trialing';
      },

      getSubscriptionStatus: () => {
        const { currentSubscription } = get();
        if (!currentSubscription) return 'free';
        return currentSubscription.status;
      },

      // Legacy methods for backward compatibility
      setSubscription: (isSubscribed: boolean, subscriptionId?: string) => {
        set({
          isSubscribed,
          subscriptionId: subscriptionId || null,
          error: null,
        });
      },

      clearSubscription: () => {
        set({
          isSubscribed: false,
          subscriptionId: null,
          subscriptionData: null,
          currentSubscription: null,
          currentPlan: null,
          usage: null,
          billingInfo: null,
          error: null,
        });
      },

      verifySubscriptionStatus: async () => {
        // Use the new loadSubscriptionData method
        await get().loadSubscriptionData();
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      checkLocalSubscription: () => {
        // Load subscription data from API
        get().loadSubscriptionData();
      },
    }),
    {
      name: 'fastcodr-subscription-store',
      partialize: (state) => ({
        // Only persist non-sensitive data
        currentPlan: state.currentPlan,
        usage: state.usage,
        isSubscribed: state.isSubscribed,
        subscriptionId: state.subscriptionId
      }),
      version: 2,
      onRehydrateStorage: () => (state) => {
        // Load fresh subscription data on app start
        state?.loadSubscriptionData();
      },
    }
  )
);

export default useSubscriptionStore;
