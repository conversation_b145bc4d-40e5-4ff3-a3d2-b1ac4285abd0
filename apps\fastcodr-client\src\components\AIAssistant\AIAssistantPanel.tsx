import React, { useState, useRef, useEffect } from 'react';
import { useChat } from 'ai/react';
import { Send, Bot, User, Code, Sparkles, X, Minimize2, Maximize2 } from 'lucide-react';
import PermissionGuard from '@/components/auth/PermissionGuard';
import { Permission } from '@/types/auth';
import useUserStore from '@/stores/userSlice';
import { toast } from 'react-hot-toast';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface AIAssistantPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onMinimize?: () => void;
  isMinimized?: boolean;
  className?: string;
}

const AIAssistantPanel: React.FC<AIAssistantPanelProps> = ({
  isOpen,
  onClose,
  onMinimize,
  isMinimized = false,
  className = ''
}) => {
  const { user, token, getRemainingUsage } = useUserStore();
  const [isExpanded, setIsExpanded] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const remainingUsage = getRemainingUsage();

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    error,
    setMessages
  } = useChat({
    api: `${process.env.APP_BASE_URL}/api/chat`,
    headers: {
      ...(token && { Authorization: `Bearer ${token}` }),
    },
    body: {
      model: 'claude-3-5-sonnet',
      mode: 'chat',
      otherConfig: {
        extra: {
          isBackEnd: false,
          backendLanguage: 'javascript'
        }
      }
    },
    onError: (error) => {
      console.error('AI Assistant error:', error);
      if (error.message.includes('AI call limit exceeded')) {
        toast.error('AI call limit exceeded. Upgrade to Premium for unlimited access!');
      } else if (error.message.includes('Authentication required')) {
        toast.error('Please log in to use the AI Assistant');
      } else {
        toast.error('AI Assistant error: ' + error.message);
      }
    },
    onFinish: () => {
      scrollToBottom();
    }
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;
    
    // Check usage limits
    if (remainingUsage.aiCalls === 0) {
      toast.error('AI call limit reached. Upgrade to Premium for unlimited access!');
      return;
    }
    
    handleSubmit(e);
  };

  const clearChat = () => {
    setMessages([]);
  };

  const insertCodeToEditor = (code: string, language: string) => {
    // This would integrate with the code editor
    // For now, just copy to clipboard
    navigator.clipboard.writeText(code);
    toast.success('Code copied to clipboard!');
  };

  if (!isOpen) return null;

  return (
    <PermissionGuard 
      requiredPermissions={[Permission.AI_ASSISTANT]}
      showUpgradePrompt={true}
    >
      <div className={`
        fixed right-4 bottom-4 bg-white dark:bg-gray-800 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 z-50
        ${isMinimized ? 'w-80 h-16' : isExpanded ? 'w-[600px] h-[700px]' : 'w-96 h-[500px]'}
        transition-all duration-300 ease-in-out
        ${className}
      `}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-t-lg">
          <div className="flex items-center gap-2">
            <Bot className="w-5 h-5" />
            <h3 className="font-semibold">AI Assistant</h3>
            <div className="flex items-center gap-1 text-xs bg-white/20 px-2 py-1 rounded-full">
              <Sparkles className="w-3 h-3" />
              Premium
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {/* Usage indicator */}
            {remainingUsage.aiCalls !== -1 && (
              <span className="text-xs bg-white/20 px-2 py-1 rounded-full">
                {remainingUsage.aiCalls} calls left
              </span>
            )}
            
            {!isMinimized && (
              <>
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="p-1 hover:bg-white/20 rounded transition-colors"
                  title={isExpanded ? 'Minimize' : 'Expand'}
                >
                  {isExpanded ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
                </button>
                
                <button
                  onClick={clearChat}
                  className="p-1 hover:bg-white/20 rounded transition-colors text-xs"
                  title="Clear chat"
                >
                  Clear
                </button>
              </>
            )}
            
            {onMinimize && (
              <button
                onClick={onMinimize}
                className="p-1 hover:bg-white/20 rounded transition-colors"
                title="Minimize"
              >
                <Minimize2 className="w-4 h-4" />
              </button>
            )}
            
            <button
              onClick={onClose}
              className="p-1 hover:bg-white/20 rounded transition-colors"
              title="Close"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {!isMinimized && (
          <>
            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4 max-h-[calc(100%-140px)]">
              {messages.length === 0 ? (
                <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                  <Bot className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p className="text-lg font-medium mb-2">AI Assistant Ready</p>
                  <p className="text-sm">Ask me anything about coding, debugging, or project help!</p>
                  
                  <div className="mt-6 grid grid-cols-1 gap-2 text-left">
                    <button
                      onClick={() => handleInputChange({ target: { value: 'Help me debug this JavaScript function' } } as any)}
                      className="p-3 text-left bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                    >
                      <div className="font-medium text-sm">Debug Code</div>
                      <div className="text-xs text-gray-500">Help me find and fix bugs</div>
                    </button>
                    
                    <button
                      onClick={() => handleInputChange({ target: { value: 'Explain this React component and suggest improvements' } } as any)}
                      className="p-3 text-left bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                    >
                      <div className="font-medium text-sm">Code Review</div>
                      <div className="text-xs text-gray-500">Get suggestions and improvements</div>
                    </button>
                    
                    <button
                      onClick={() => handleInputChange({ target: { value: 'Generate a REST API endpoint for user authentication' } } as any)}
                      className="p-3 text-left bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                    >
                      <div className="font-medium text-sm">Generate Code</div>
                      <div className="text-xs text-gray-500">Create new code snippets</div>
                    </button>
                  </div>
                </div>
              ) : (
                messages.map((message) => (
                  <MessageBubble
                    key={message.id}
                    message={message}
                    onInsertCode={insertCodeToEditor}
                  />
                ))
              )}
              
              {isLoading && (
                <div className="flex items-center gap-2 text-gray-500">
                  <Bot className="w-4 h-4" />
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* Input */}
            <form onSubmit={handleFormSubmit} className="p-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={input}
                  onChange={handleInputChange}
                  placeholder="Ask me anything about coding..."
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  disabled={isLoading || remainingUsage.aiCalls === 0}
                />
                <button
                  type="submit"
                  disabled={isLoading || !input.trim() || remainingUsage.aiCalls === 0}
                  className="px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center gap-2"
                >
                  <Send className="w-4 h-4" />
                </button>
              </div>
              
              {remainingUsage.aiCalls === 0 && (
                <div className="mt-2 text-xs text-red-500">
                  AI call limit reached. <button onClick={() => window.location.href = '/subscribe'} className="underline">Upgrade to Premium</button> for unlimited access.
                </div>
              )}
            </form>
          </>
        )}
      </div>
    </PermissionGuard>
  );
};

interface MessageBubbleProps {
  message: any;
  onInsertCode: (code: string, language: string) => void;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message, onInsertCode }) => {
  const isUser = message.role === 'user';
  
  const renderContent = (content: string) => {
    // Simple code block detection
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    const parts = [];
    let lastIndex = 0;
    let match;

    while ((match = codeBlockRegex.exec(content)) !== null) {
      // Add text before code block
      if (match.index > lastIndex) {
        parts.push(
          <span key={lastIndex} className="whitespace-pre-wrap">
            {content.slice(lastIndex, match.index)}
          </span>
        );
      }

      // Add code block
      const language = match[1] || 'text';
      const code = match[2];
      parts.push(
        <div key={match.index} className="my-3">
          <div className="flex items-center justify-between bg-gray-800 text-white px-3 py-2 rounded-t-lg text-sm">
            <span>{language}</span>
            <button
              onClick={() => onInsertCode(code, language)}
              className="flex items-center gap-1 text-xs hover:bg-gray-700 px-2 py-1 rounded transition-colors"
            >
              <Code className="w-3 h-3" />
              Insert
            </button>
          </div>
          <SyntaxHighlighter
            language={language}
            style={vscDarkPlus}
            customStyle={{
              margin: 0,
              borderTopLeftRadius: 0,
              borderTopRightRadius: 0,
            }}
          >
            {code}
          </SyntaxHighlighter>
        </div>
      );

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < content.length) {
      parts.push(
        <span key={lastIndex} className="whitespace-pre-wrap">
          {content.slice(lastIndex)}
        </span>
      );
    }

    return parts.length > 0 ? parts : <span className="whitespace-pre-wrap">{content}</span>;
  };

  return (
    <div className={`flex gap-3 ${isUser ? 'flex-row-reverse' : ''}`}>
      <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
        isUser ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
      }`}>
        {isUser ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
      </div>
      
      <div className={`max-w-[80%] p-3 rounded-lg ${
        isUser 
          ? 'bg-blue-500 text-white' 
          : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100'
      }`}>
        <div className="text-sm">
          {renderContent(message.content)}
        </div>
      </div>
    </div>
  );
};

export default AIAssistantPanel;
