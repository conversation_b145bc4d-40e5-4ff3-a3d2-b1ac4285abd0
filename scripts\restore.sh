#!/bin/bash

# FastCodr Restore Script
# This script restores backups of the database and application files

set -e

# Configuration
BACKUP_DIR="/backups"
RESTORE_DATE=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Usage function
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -d, --date DATE     Restore from specific date (format: YYYYMMDD_HHMMSS)"
    echo "  -l, --list          List available backups"
    echo "  -h, --help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --list                           # List all available backups"
    echo "  $0 --date 20231201_143000          # Restore from specific backup"
    echo "  $0                                  # Restore from latest backup"
}

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to list available backups
list_backups() {
    log "Available backups in $BACKUP_DIR:"
    echo ""
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_error "Backup directory does not exist: $BACKUP_DIR"
        return 1
    fi
    
    # List MongoDB backups
    echo "MongoDB Backups:"
    ls -la "$BACKUP_DIR"/mongodb_backup_*.tar.gz 2>/dev/null | awk '{print "  " $9 " (" $5 " bytes, " $6 " " $7 " " $8 ")"}' || echo "  No MongoDB backups found"
    echo ""
    
    # List Application backups
    echo "Application Backups:"
    ls -la "$BACKUP_DIR"/application_backup_*.tar.gz 2>/dev/null | awk '{print "  " $9 " (" $5 " bytes, " $6 " " $7 " " $8 ")"}' || echo "  No application backups found"
    echo ""
    
    # List Redis backups
    echo "Redis Backups:"
    ls -la "$BACKUP_DIR"/redis_backup_*.rdb 2>/dev/null | awk '{print "  " $9 " (" $5 " bytes, " $6 " " $7 " " $8 ")"}' || echo "  No Redis backups found"
    echo ""
    
    # List SSL backups
    echo "SSL Certificate Backups:"
    ls -la "$BACKUP_DIR"/ssl_backup_*.tar.gz 2>/dev/null | awk '{print "  " $9 " (" $5 " bytes, " $6 " " $7 " " $8 ")"}' || echo "  No SSL backups found"
}

# Function to get latest backup date
get_latest_backup_date() {
    local latest_backup=$(ls -t "$BACKUP_DIR"/mongodb_backup_*.tar.gz 2>/dev/null | head -1)
    if [ -n "$latest_backup" ]; then
        basename "$latest_backup" | sed 's/mongodb_backup_\(.*\)\.tar\.gz/\1/'
    else
        echo ""
    fi
}

# Function to confirm restore operation
confirm_restore() {
    local backup_date=$1
    
    log_warning "This will restore FastCodr from backup dated: $backup_date"
    log_warning "Current data will be OVERWRITTEN and LOST!"
    echo ""
    read -p "Are you sure you want to continue? (yes/no): " confirm
    
    if [ "$confirm" != "yes" ]; then
        log "Restore operation cancelled"
        exit 0
    fi
}

# Function to stop services
stop_services() {
    log "Stopping FastCodr services..."
    
    docker-compose down 2>/dev/null || log_warning "Services were not running"
    
    log_success "Services stopped"
}

# Function to restore MongoDB
restore_mongodb() {
    local backup_date=$1
    local mongodb_backup="$BACKUP_DIR/mongodb_backup_$backup_date.tar.gz"
    
    log "Restoring MongoDB from: $mongodb_backup"
    
    if [ ! -f "$mongodb_backup" ]; then
        log_error "MongoDB backup file not found: $mongodb_backup"
        return 1
    fi
    
    # Extract backup
    local temp_dir="/tmp/mongodb_restore_$backup_date"
    mkdir -p "$temp_dir"
    tar -xzf "$mongodb_backup" -C "$temp_dir"
    
    # Start only MongoDB service
    docker-compose up -d mongodb
    sleep 10  # Wait for MongoDB to start
    
    # Restore the database
    docker-compose exec -T mongodb mongorestore \
        --drop \
        --gzip \
        --verbose \
        "/backups/mongodb_$backup_date"
    
    if [ $? -eq 0 ]; then
        log_success "MongoDB restore completed"
        rm -rf "$temp_dir"
    else
        log_error "MongoDB restore failed"
        rm -rf "$temp_dir"
        return 1
    fi
}

# Function to restore application files
restore_application() {
    local backup_date=$1
    local app_backup="$BACKUP_DIR/application_backup_$backup_date.tar.gz"
    
    log "Restoring application files from: $app_backup"
    
    if [ ! -f "$app_backup" ]; then
        log_error "Application backup file not found: $app_backup"
        return 1
    fi
    
    # Create backup of current application files
    local current_backup="/tmp/current_app_backup_$(date +%Y%m%d_%H%M%S).tar.gz"
    tar -czf "$current_backup" \
        --exclude='node_modules' \
        --exclude='dist' \
        --exclude='build' \
        --exclude='.git' \
        --exclude='logs' \
        apps/ docker-compose*.yml nginx/ monitoring/ scripts/ .env* README.md DEPLOYMENT.md 2>/dev/null || true
    
    log "Current application files backed up to: $current_backup"
    
    # Restore application files
    tar -xzf "$app_backup" -C /
    
    if [ $? -eq 0 ]; then
        log_success "Application files restore completed"
    else
        log_error "Application files restore failed"
        return 1
    fi
}

# Function to restore Redis
restore_redis() {
    local backup_date=$1
    local redis_backup="$BACKUP_DIR/redis_backup_$backup_date.rdb"
    
    log "Restoring Redis from: $redis_backup"
    
    if [ ! -f "$redis_backup" ]; then
        log_warning "Redis backup file not found: $redis_backup (skipping)"
        return 0
    fi
    
    # Start only Redis service
    docker-compose up -d redis
    sleep 5  # Wait for Redis to start
    
    # Stop Redis to replace the dump file
    docker-compose stop redis
    
    # Copy the backup file
    docker cp "$redis_backup" $(docker-compose ps -q redis):/data/dump.rdb
    
    # Start Redis again
    docker-compose start redis
    
    if [ $? -eq 0 ]; then
        log_success "Redis restore completed"
    else
        log_error "Redis restore failed"
        return 1
    fi
}

# Function to restore SSL certificates
restore_ssl() {
    local backup_date=$1
    local ssl_backup="$BACKUP_DIR/ssl_backup_$backup_date.tar.gz"
    
    log "Restoring SSL certificates from: $ssl_backup"
    
    if [ ! -f "$ssl_backup" ]; then
        log_warning "SSL backup file not found: $ssl_backup (skipping)"
        return 0
    fi
    
    # Backup current SSL certificates
    if [ -d "nginx/ssl" ]; then
        local current_ssl_backup="/tmp/current_ssl_backup_$(date +%Y%m%d_%H%M%S).tar.gz"
        tar -czf "$current_ssl_backup" nginx/ssl/
        log "Current SSL certificates backed up to: $current_ssl_backup"
    fi
    
    # Restore SSL certificates
    tar -xzf "$ssl_backup" -C /
    
    if [ $? -eq 0 ]; then
        log_success "SSL certificates restore completed"
    else
        log_error "SSL certificates restore failed"
        return 1
    fi
}

# Function to start services
start_services() {
    log "Starting FastCodr services..."
    
    docker-compose up -d
    
    # Wait for services to be ready
    sleep 30
    
    # Check service health
    if docker-compose ps | grep -q "Up"; then
        log_success "Services started successfully"
    else
        log_error "Some services failed to start"
        docker-compose ps
        return 1
    fi
}

# Function to verify restore
verify_restore() {
    log "Verifying restore..."
    
    # Check if services are running
    if ! docker-compose ps | grep -q "Up"; then
        log_error "Services are not running properly"
        return 1
    fi
    
    # Test backend health
    if curl -f http://localhost:3001/api/health >/dev/null 2>&1; then
        log_success "Backend health check passed"
    else
        log_warning "Backend health check failed (may need more time to start)"
    fi
    
    # Test frontend
    if curl -f http://localhost:80 >/dev/null 2>&1; then
        log_success "Frontend health check passed"
    else
        log_warning "Frontend health check failed (may need more time to start)"
    fi
    
    log_success "Restore verification completed"
}

# Main restore function
main() {
    local backup_date="$RESTORE_DATE"
    
    # If no date specified, use latest backup
    if [ -z "$backup_date" ]; then
        backup_date=$(get_latest_backup_date)
        if [ -z "$backup_date" ]; then
            log_error "No backups found in $BACKUP_DIR"
            exit 1
        fi
        log "Using latest backup: $backup_date"
    fi
    
    # Confirm restore operation
    confirm_restore "$backup_date"
    
    log "=== FastCodr Restore Started ==="
    log "Restore date: $backup_date"
    log "Backup directory: $BACKUP_DIR"
    
    local restore_success=true
    
    # Stop services
    stop_services || restore_success=false
    
    # Perform restore
    restore_mongodb "$backup_date" || restore_success=false
    restore_application "$backup_date" || restore_success=false
    restore_redis "$backup_date" || restore_success=false
    restore_ssl "$backup_date" || restore_success=false
    
    # Start services
    start_services || restore_success=false
    
    # Verify restore
    verify_restore || restore_success=false
    
    if [ "$restore_success" = true ]; then
        log_success "=== FastCodr Restore Completed Successfully ==="
        log_success "Application restored from backup: $backup_date"
    else
        log_error "=== FastCodr Restore Completed with Errors ==="
        log_error "Some components may not have been restored properly"
        exit 1
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--date)
            RESTORE_DATE="$2"
            shift 2
            ;;
        -l|--list)
            list_backups
            exit 0
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Check if running as root or with sudo
if [ "$EUID" -eq 0 ]; then
    log_warning "Running as root. Consider running with appropriate user permissions."
fi

# Run main function
main "$@"
