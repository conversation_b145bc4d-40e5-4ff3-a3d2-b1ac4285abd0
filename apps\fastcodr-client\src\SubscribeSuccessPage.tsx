import React, { useEffect, useState } from 'react';
import { setSubscriptionStatus } from './lib/cashfree';
import useSubscriptionStore from './stores/subscriptionSlice';
import { CheckCircle, Crown, Sparkles, ArrowRight } from 'lucide-react';

export default function SubscribeSuccessPage() {
  const { setSubscription } = useSubscriptionStore();
  const [isActivating, setIsActivating] = useState(true);

  useEffect(() => {
    // Activate subscription
    const activateSubscription = async () => {
      try {
        // Get pending subscription ID from localStorage
        const pendingSubscriptionId = localStorage.getItem('pending_subscription_id');

        // Mark subscription as active (both new store and legacy method)
        setSubscription(true, pendingSubscriptionId || undefined);
        setSubscriptionStatus(true, pendingSubscriptionId || undefined);

        // Clean up pending subscription ID
        localStorage.removeItem('pending_subscription_id');

        // Simulate activation delay for better UX
        setTimeout(() => {
          setIsActivating(false);
        }, 2000);
      } catch (error) {
        console.error('Error activating subscription:', error);
        setIsActivating(false);
      }
    };

    activateSubscription();
  }, [setSubscription]);

  const handleContinue = () => {
    window.location.href = '/';
  };

  if (isActivating) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 dark:from-green-950 dark:via-blue-950 dark:to-purple-950">
        <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-8 max-w-md w-full text-center">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 border-4 border-green-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
          <h1 className="text-2xl font-bold mb-4 text-gray-800 dark:text-gray-200">
            Activating Your Premium Account...
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Please wait while we set up your premium features.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 dark:from-green-950 dark:via-blue-950 dark:to-purple-950">
      <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-8 max-w-lg w-full text-center">
        {/* Success Icon */}
        <div className="flex items-center justify-center mb-6">
          <div className="relative">
            <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center">
              <CheckCircle className="w-12 h-12 text-white" />
            </div>
            <div className="absolute -top-2 -right-2">
              <Crown className="w-8 h-8 text-yellow-500" />
            </div>
          </div>
        </div>

        {/* Success Message */}
        <h1 className="text-3xl font-bold mb-4 bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
          Welcome to FastCodr Premium!
        </h1>

        <p className="text-lg text-gray-600 dark:text-gray-400 mb-6">
          🎉 Your subscription is now active! You now have access to all premium features.
        </p>

        {/* Premium Features Unlocked */}
        <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/20 dark:to-blue-950/20 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-center gap-2 mb-3">
            <Sparkles className="w-5 h-5 text-yellow-500" />
            <span className="font-semibold text-gray-800 dark:text-gray-200">Premium Features Unlocked</span>
          </div>
          <div className="grid grid-cols-2 gap-2 text-sm text-gray-600 dark:text-gray-400">
            <div>✨ Unlimited AI generation</div>
            <div>🚀 Advanced debugging</div>
            <div>📚 Premium templates</div>
            <div>🎯 Priority support</div>
          </div>
        </div>

        {/* CTA Button */}
        <button
          onClick={handleContinue}
          className="w-full py-4 px-6 bg-gradient-to-r from-green-600 to-blue-600 text-white font-semibold text-lg rounded-lg shadow-lg hover:from-green-700 hover:to-blue-700 transition-all duration-200 transform hover:scale-105"
        >
          <div className="flex items-center justify-center gap-2">
            Start Coding with Premium
            <ArrowRight className="w-5 h-5" />
          </div>
        </button>

        <p className="text-xs text-gray-500 dark:text-gray-400 mt-4">
          You can manage your subscription anytime in your account settings.
        </p>
      </div>
    </div>
  );
}
