{"name": "fastcodr-next", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^1.0.11", "@ai-sdk/deepseek": "0.1.10", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@stripe/stripe-js": "^6.1.0", "@tanstack/react-query": "^5.66.7", "@types/mdx": "^2.0.13", "accept-language": "^3.0.20", "ai": "4.1.41", "antd": "^5.23.0", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.18.0", "fumadocs-core": "^14.7.4", "fumadocs-mdx": "^11.3.1", "fumadocs-ui": "^14.7.4", "gsap": "^3.12.5", "jose": "^5.9.6", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.471.1", "minio": "^8.0.4", "mongoose": "^8.9.3", "next": "14.2.23", "next-intl": "^3.26.3", "react": "^18.3.1", "react-custom-scroll": "^7.0.0", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "stripe": "^17.7.0", "tailwind-merge": "^2.6.0", "uuid": "^11.0.3", "uuidv4": "^6.2.13", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@iconify/json": "^2.2.295", "@iconify/tailwind": "^1.2.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/stripe": "^8.0.417", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.1.2", "postcss": "^8", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}