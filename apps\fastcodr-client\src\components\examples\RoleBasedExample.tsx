import React from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import PermissionGuard, { usePermissions, withPermissions } from '@/components/auth/PermissionGuard';
import RoleBadge from '@/components/auth/RoleBadge';
import useUserStore from '@/stores/userSlice';
import { UserRole, Permission } from '@/types/auth';
import { Crown, Shield, Settings, Code, Zap } from 'lucide-react';

// Example component showing role-based access usage
const RoleBasedExample: React.FC = () => {
  const { user, getUserRole, isAdmin, isSubscriber, getRemainingUsage } = useUserStore();
  const { hasPermission, checkRole } = usePermissions();
  const remainingUsage = getRemainingUsage();

  if (!user) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-600">Please log in to see role-based features</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* User Info Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Role-Based Access Demo
          </h2>
          <RoleBadge role={user.role} size="lg" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Current Role</h3>
            <p className="text-gray-600 dark:text-gray-300">{user.role}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Permissions</h3>
            <p className="text-gray-600 dark:text-gray-300">{user.permissions.length} permissions</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Usage Status</h3>
            <p className="text-gray-600 dark:text-gray-300">
              {remainingUsage.aiCalls === -1 ? 'Unlimited' : `${remainingUsage.aiCalls} AI calls left`}
            </p>
          </div>
        </div>
      </div>

      {/* Feature Access Examples */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        
        {/* Basic Features (All Users) */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex items-center gap-3 mb-4">
            <Code className="w-6 h-6 text-blue-500" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Basic Features
            </h3>
          </div>
          
          <div className="space-y-3">
            <PermissionGuard requiredPermissions={[Permission.VIEW_EDITOR]}>
              <div className="flex items-center gap-2 text-green-600">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                Code Editor Access
              </div>
            </PermissionGuard>
            
            <PermissionGuard requiredPermissions={[Permission.SAVE_PROJECT]}>
              <div className="flex items-center gap-2 text-green-600">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                Save Projects (Limited)
              </div>
            </PermissionGuard>
          </div>
        </div>

        {/* Premium Features (Subscribers Only) */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex items-center gap-3 mb-4">
            <Crown className="w-6 h-6 text-yellow-500" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Premium Features
            </h3>
          </div>
          
          <div className="space-y-3">
            <PermissionGuard 
              requiredPermissions={[Permission.AI_ASSISTANT]}
              fallback={
                <div className="flex items-center gap-2 text-gray-400">
                  <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                  AI Assistant (Premium Only)
                </div>
              }
            >
              <div className="flex items-center gap-2 text-green-600">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                AI Assistant
              </div>
            </PermissionGuard>
            
            <PermissionGuard 
              requiredPermissions={[Permission.UNLIMITED_PROJECTS]}
              fallback={
                <div className="flex items-center gap-2 text-gray-400">
                  <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                  Unlimited Projects (Premium Only)
                </div>
              }
            >
              <div className="flex items-center gap-2 text-green-600">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                Unlimited Projects
              </div>
            </PermissionGuard>
            
            <PermissionGuard 
              requiredPermissions={[Permission.EXPORT_PROJECTS]}
              fallback={
                <div className="flex items-center gap-2 text-gray-400">
                  <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                  Export Projects (Premium Only)
                </div>
              }
            >
              <div className="flex items-center gap-2 text-green-600">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                Export Projects
              </div>
            </PermissionGuard>
          </div>
        </div>

        {/* Admin Features (Admins Only) */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex items-center gap-3 mb-4">
            <Shield className="w-6 h-6 text-purple-500" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Admin Features
            </h3>
          </div>
          
          <div className="space-y-3">
            <PermissionGuard 
              requiredPermissions={[Permission.ADMIN_DASHBOARD]}
              fallback={
                <div className="flex items-center gap-2 text-gray-400">
                  <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                  Admin Dashboard (Admin Only)
                </div>
              }
            >
              <div className="flex items-center gap-2 text-green-600">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                Admin Dashboard
              </div>
            </PermissionGuard>
            
            <PermissionGuard 
              requiredPermissions={[Permission.MANAGE_USERS]}
              fallback={
                <div className="flex items-center gap-2 text-gray-400">
                  <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                  User Management (Admin Only)
                </div>
              }
            >
              <div className="flex items-center gap-2 text-green-600">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                User Management
              </div>
            </PermissionGuard>
          </div>
        </div>

        {/* Usage Limits */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex items-center gap-3 mb-4">
            <Zap className="w-6 h-6 text-orange-500" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Usage Limits
            </h3>
          </div>
          
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-300">Projects</span>
              <span className="font-medium">
                {remainingUsage.projects === -1 ? 'Unlimited' : `${remainingUsage.projects} left`}
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-300">AI Calls</span>
              <span className="font-medium">
                {remainingUsage.aiCalls === -1 ? 'Unlimited' : `${remainingUsage.aiCalls} left`}
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-300">Storage</span>
              <span className="font-medium">
                {remainingUsage.storage === -1 ? 'Unlimited' : `${remainingUsage.storage} MB left`}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons with Role Checks */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Role-Based Actions
        </h3>
        
        <div className="flex flex-wrap gap-3">
          {/* Always visible button */}
          <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
            Basic Action
          </button>
          
          {/* Subscriber-only button */}
          <PermissionGuard 
            requiredPermissions={[Permission.AI_ASSISTANT]}
            fallback={
              <button 
                disabled 
                className="bg-gray-300 text-gray-500 px-4 py-2 rounded-lg cursor-not-allowed"
              >
                AI Assistant (Premium)
              </button>
            }
          >
            <button className="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg transition-colors">
              Use AI Assistant
            </button>
          </PermissionGuard>
          
          {/* Admin-only button */}
          <PermissionGuard 
            requiredPermissions={[Permission.ADMIN_DASHBOARD]}
            fallback={
              <button 
                disabled 
                className="bg-gray-300 text-gray-500 px-4 py-2 rounded-lg cursor-not-allowed"
              >
                Admin Panel (Admin Only)
              </button>
            }
          >
            <button className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors">
              Open Admin Panel
            </button>
          </PermissionGuard>
        </div>
      </div>

      {/* Upgrade Prompt for Free Users */}
      {user.role === UserRole.USER && (
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
          <div className="flex items-center gap-3 mb-3">
            <Crown className="w-6 h-6 text-yellow-500" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Unlock Premium Features
            </h3>
          </div>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Upgrade to FastCodr Premium to access AI assistance, unlimited projects, and more!
          </p>
          <button 
            onClick={() => window.location.href = '/subscribe'}
            className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white px-6 py-2 rounded-lg transition-all duration-200 transform hover:scale-105"
          >
            Upgrade Now
          </button>
        </div>
      )}
    </div>
  );
};

// Example of using withPermissions HOC
const AdminOnlyComponent = withPermissions(
  () => (
    <div className="p-4 bg-purple-100 rounded-lg">
      <h4 className="font-semibold text-purple-900">Admin Only Component</h4>
      <p className="text-purple-700">This component is only visible to admins</p>
    </div>
  ),
  UserRole.ADMIN,
  [Permission.ADMIN_DASHBOARD]
);

export default RoleBasedExample;
