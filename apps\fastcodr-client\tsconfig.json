{
  "compilerOptions": {
    "noImplicitAny": false,
    "module": "ESNext",
    "strict": false,
    "jsx": "react-jsx",
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "lib": [
      "DOM",
      "DOM.Iterable",
      "ESNext"
    ],
    "types": [
      "vite/client"
    ],
    "moduleResolution": "Bundler",
    "outDir": "./dist",
    "baseUrl": ".",
    "paths": {
      "~/*": [
        "../../components/*"
      ],
      "@/*": [
        "./src/*"
      ]
    }
  },
  "exclude": [
    "**/**/node_modules/*",
    "**/**/58d2c/*",
    "**/**/SketchEditor/*"
  ],
  "include": [
    "src",
    "electron",
    "src/types/*.d.ts"
  ],
}
