import React, { useState, useEffect } from 'react';
import { Lightbulb, Code, Zap, Shield, TestTube, MessageSquare, X } from 'lucide-react';
import { useAIAssistant, AI_PROMPT_TEMPLATES } from '@/hooks/useAIAssistant';
import PermissionGuard from '@/components/auth/PermissionGuard';
import { Permission } from '@/types/auth';
import { toast } from 'react-hot-toast';

interface AISuggestionsProps {
  selectedCode: string;
  onInsertCode?: (code: string) => void;
  onClose?: () => void;
  className?: string;
}

const AISuggestions: React.FC<AISuggestionsProps> = ({
  selectedCode,
  onInsertCode,
  onClose,
  className = ''
}) => {
  const [activeAction, setActiveAction] = useState<string | null>(null);
  const [suggestions, setSuggestions] = useState<string>('');
  const [isExpanded, setIsExpanded] = useState(false);
  
  const {
    isLoading,
    canUseAI,
    remainingCalls,
    explainCode,
    debugCode,
    optimizeCode,
    refactorCode,
    generateTests,
    securityReview,
    getLatestResponse,
    extractCodeBlocks
  } = useAIAssistant({
    onSuccess: (response) => {
      setSuggestions(response);
      setIsExpanded(true);
    },
    onError: (error) => {
      setActiveAction(null);
      setSuggestions('');
    }
  });

  const quickActions = [
    {
      id: 'explain',
      label: 'Explain',
      icon: <MessageSquare className="w-4 h-4" />,
      action: explainCode,
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      id: 'debug',
      label: 'Debug',
      icon: <Shield className="w-4 h-4" />,
      action: debugCode,
      color: 'bg-red-500 hover:bg-red-600'
    },
    {
      id: 'optimize',
      label: 'Optimize',
      icon: <Zap className="w-4 h-4" />,
      action: optimizeCode,
      color: 'bg-yellow-500 hover:bg-yellow-600'
    },
    {
      id: 'refactor',
      label: 'Refactor',
      icon: <Code className="w-4 h-4" />,
      action: refactorCode,
      color: 'bg-green-500 hover:bg-green-600'
    },
    {
      id: 'test',
      label: 'Tests',
      icon: <TestTube className="w-4 h-4" />,
      action: generateTests,
      color: 'bg-purple-500 hover:bg-purple-600'
    },
    {
      id: 'security',
      label: 'Security',
      icon: <Shield className="w-4 h-4" />,
      action: securityReview,
      color: 'bg-orange-500 hover:bg-orange-600'
    }
  ];

  const handleAction = async (actionId: string, actionFn: (code: string) => Promise<any>) => {
    if (!canUseAI) return;
    
    setActiveAction(actionId);
    setSuggestions('');
    setIsExpanded(false);
    
    try {
      await actionFn(selectedCode);
    } catch (error) {
      console.error('AI action failed:', error);
      setActiveAction(null);
    }
  };

  const handleInsertCode = () => {
    const codeBlocks = extractCodeBlocks(suggestions);
    if (codeBlocks.length > 0 && onInsertCode) {
      onInsertCode(codeBlocks[0].code);
      toast.success('Code inserted successfully!');
    } else {
      toast.error('No code found to insert');
    }
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(suggestions);
    toast.success('Response copied to clipboard!');
  };

  if (!selectedCode) return null;

  return (
    <PermissionGuard requiredPermissions={[Permission.AI_ASSISTANT]}>
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 ${className}`}>
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2">
            <Lightbulb className="w-4 h-4 text-yellow-500" />
            <span className="font-medium text-sm">AI Suggestions</span>
            {remainingCalls !== -1 && (
              <span className="text-xs text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
                {remainingCalls} calls left
              </span>
            )}
          </div>
          
          {onClose && (
            <button
              onClick={onClose}
              className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>

        {/* Selected Code Preview */}
        <div className="p-3 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
          <div className="text-xs text-gray-500 mb-1">Selected Code:</div>
          <div className="text-xs font-mono bg-white dark:bg-gray-800 p-2 rounded border max-h-20 overflow-y-auto">
            {selectedCode.length > 100 ? selectedCode.substring(0, 100) + '...' : selectedCode}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="p-3">
          <div className="grid grid-cols-3 gap-2 mb-3">
            {quickActions.map((action) => (
              <button
                key={action.id}
                onClick={() => handleAction(action.id, action.action)}
                disabled={!canUseAI || isLoading}
                className={`
                  ${action.color} text-white text-xs px-3 py-2 rounded transition-colors
                  disabled:opacity-50 disabled:cursor-not-allowed
                  flex items-center justify-center gap-1
                  ${activeAction === action.id ? 'ring-2 ring-white ring-opacity-50' : ''}
                `}
              >
                {isLoading && activeAction === action.id ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  action.icon
                )}
                {action.label}
              </button>
            ))}
          </div>

          {/* Loading State */}
          {isLoading && activeAction && (
            <div className="flex items-center gap-2 text-gray-500 text-sm py-2">
              <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
              Processing your request...
            </div>
          )}

          {/* Suggestions Display */}
          {suggestions && (
            <div className="mt-3 border-t border-gray-200 dark:border-gray-700 pt-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">AI Response:</span>
                <div className="flex gap-2">
                  {extractCodeBlocks(suggestions).length > 0 && onInsertCode && (
                    <button
                      onClick={handleInsertCode}
                      className="text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded transition-colors"
                    >
                      Insert Code
                    </button>
                  )}
                  <button
                    onClick={handleCopy}
                    className="text-xs bg-gray-500 hover:bg-gray-600 text-white px-2 py-1 rounded transition-colors"
                  >
                    Copy
                  </button>
                  <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="text-xs bg-gray-300 hover:bg-gray-400 text-gray-700 px-2 py-1 rounded transition-colors"
                  >
                    {isExpanded ? 'Collapse' : 'Expand'}
                  </button>
                </div>
              </div>
              
              <div className={`
                text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-900 p-3 rounded border
                ${isExpanded ? 'max-h-none' : 'max-h-32'} overflow-y-auto whitespace-pre-wrap
              `}>
                {suggestions}
              </div>
            </div>
          )}

          {/* Usage Limit Warning */}
          {remainingCalls !== -1 && remainingCalls <= 5 && (
            <div className="mt-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded text-xs">
              <div className="flex items-center gap-1 text-yellow-700 dark:text-yellow-300">
                <Lightbulb className="w-3 h-3" />
                Low on AI calls ({remainingCalls} remaining)
              </div>
              <button
                onClick={() => window.location.href = '/subscribe'}
                className="text-yellow-600 dark:text-yellow-400 underline hover:no-underline"
              >
                Upgrade to Premium for unlimited access
              </button>
            </div>
          )}
        </div>
      </div>
    </PermissionGuard>
  );
};

export default AISuggestions;
