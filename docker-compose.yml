version: '3.8'

services:
  # FastCodr Backend (Next.js API)
  backend:
    build:
      context: ./apps/fastcodr-next
      dockerfile: Dockerfile
    container_name: fastcodr-backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
    env_file:
      - ./apps/fastcodr-next/.env.production
    depends_on:
      - mongodb
    restart: unless-stopped
    networks:
      - fastcodr-network

  # FastCodr Frontend (React + Vite)
  frontend:
    build:
      context: ./apps/fastcodr-client
      dockerfile: Dockerfile
    container_name: fastcodr-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - fastcodr-network

  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: fastcodr-mongodb
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=fastcodr
      - MONGO_INITDB_ROOT_PASSWORD=your_secure_password
      - MONGO_INITDB_DATABASE=fastcodr
    volumes:
      - mongodb_data:/data/db
      - ./mongodb-init:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - fastcodr-network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: fastcodr-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - fastcodr-network

  # Nginx Load Balancer (optional)
  nginx:
    image: nginx:alpine
    container_name: fastcodr-nginx
    ports:
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    networks:
      - fastcodr-network

volumes:
  mongodb_data:
  redis_data:

networks:
  fastcodr-network:
    driver: bridge
