"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/model/route";
exports.ids = ["app/api/model/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmodel%2Froute&page=%2Fapi%2Fmodel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodel%2Froute.ts&appDir=C%3A%5CUsers%5Cjay%20prakash%5COneDrive%5CDesktop%5Cjitender%5CJitender%20Works%5Cfastcodr%5Capps%5Cfastcodr-next%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5Cjay%20prakash%5COneDrive%5CDesktop%5Cjitender%5CJitender%20Works%5Cfastcodr%5Capps%5Cfastcodr-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmodel%2Froute&page=%2Fapi%2Fmodel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodel%2Froute.ts&appDir=C%3A%5CUsers%5Cjay%20prakash%5COneDrive%5CDesktop%5Cjitender%5CJitender%20Works%5Cfastcodr%5Capps%5Cfastcodr-next%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5Cjay%20prakash%5COneDrive%5CDesktop%5Cjitender%5CJitender%20Works%5Cfastcodr%5Capps%5Cfastcodr-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_jay_prakash_OneDrive_Desktop_jitender_Jitender_Works_fastcodr_apps_fastcodr_next_app_api_model_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/model/route.ts */ \"(rsc)/./app/api/model/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/model/route\",\n        pathname: \"/api/model\",\n        filename: \"route\",\n        bundlePath: \"app/api/model/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\jitender\\\\Jitender Works\\\\fastcodr\\\\apps\\\\fastcodr-next\\\\app\\\\api\\\\model\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_jay_prakash_OneDrive_Desktop_jitender_Jitender_Works_fastcodr_apps_fastcodr_next_app_api_model_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/model/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZtb2RlbCUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGbW9kZWwlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZtb2RlbCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNqYXklMjBwcmFrYXNoJTVDT25lRHJpdmUlNUNEZXNrdG9wJTVDaml0ZW5kZXIlNUNKaXRlbmRlciUyMFdvcmtzJTVDZmFzdGNvZHIlNUNhcHBzJTVDZmFzdGNvZHItbmV4dCU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz1tZHgmcGFnZUV4dGVuc2lvbnM9bWQmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q2pheSUyMHByYWthc2glNUNPbmVEcml2ZSU1Q0Rlc2t0b3AlNUNqaXRlbmRlciU1Q0ppdGVuZGVyJTIwV29ya3MlNUNmYXN0Y29kciU1Q2FwcHMlNUNmYXN0Y29kci1uZXh0JmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDYztBQUM2RTtBQUMxSjtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsZ0hBQW1CO0FBQzNDO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLGlFQUFpRTtBQUN6RTtBQUNBO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ3VIOztBQUV2SCIsInNvdXJjZXMiOlsid2VicGFjazovL2Zhc3Rjb2RyLW5leHQvPzNmMTQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcamF5IHByYWthc2hcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxqaXRlbmRlclxcXFxKaXRlbmRlciBXb3Jrc1xcXFxmYXN0Y29kclxcXFxhcHBzXFxcXGZhc3Rjb2RyLW5leHRcXFxcYXBwXFxcXGFwaVxcXFxtb2RlbFxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvbW9kZWwvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9tb2RlbFwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvbW9kZWwvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxVc2Vyc1xcXFxqYXkgcHJha2FzaFxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXGppdGVuZGVyXFxcXEppdGVuZGVyIFdvcmtzXFxcXGZhc3Rjb2RyXFxcXGFwcHNcXFxcZmFzdGNvZHItbmV4dFxcXFxhcHBcXFxcYXBpXFxcXG1vZGVsXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL2FwaS9tb2RlbC9yb3V0ZVwiO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICBzZXJ2ZXJIb29rcyxcbiAgICAgICAgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBvcmlnaW5hbFBhdGhuYW1lLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmodel%2Froute&page=%2Fapi%2Fmodel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodel%2Froute.ts&appDir=C%3A%5CUsers%5Cjay%20prakash%5COneDrive%5CDesktop%5Cjitender%5CJitender%20Works%5Cfastcodr%5Capps%5Cfastcodr-next%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5Cjay%20prakash%5COneDrive%5CDesktop%5Cjitender%5CJitender%20Works%5Cfastcodr%5Capps%5Cfastcodr-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/model/config.ts":
/*!*********************************!*\
  !*** ./app/api/model/config.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   modelConfig: () => (/* binding */ modelConfig)\n/* harmony export */ });\n// Model configuration file\n// Configure models based on actual scenarios\nconst modelConfig = [\n    {\n        modelName: \"claude-3-5-sonnet\",\n        modelKey: \"claude-3-5-sonnet-20240620\",\n        useImage: true,\n        provider: \"claude\",\n        description: \"Claude 3.5 Sonnet model\",\n        functionCall: true\n    },\n    {\n        modelName: \"gpt-4o-mini\",\n        modelKey: \"gpt-4o-mini\",\n        useImage: false,\n        provider: \"openai\",\n        description: \"GPT-4 Optimized Mini model\",\n        functionCall: true\n    },\n    {\n        modelName: \"deepseek-R1\",\n        modelKey: \"deepseek-reasoner\",\n        useImage: false,\n        provider: \"deepseek\",\n        description: \"Deepseek R1 model with reasoning and chain-of-thought capabilities\",\n        functionCall: false\n    },\n    {\n        modelName: \"deepseek-v3\",\n        modelKey: \"deepseek-chat\",\n        useImage: false,\n        provider: \"deepseek\",\n        description: \"Deepseek V3 model\",\n        functionCall: true\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL21vZGVsL2NvbmZpZy50cyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsMkJBQTJCO0FBQzNCLDZDQUE2QztBQWN0QyxNQUFNQSxjQUE2QjtJQUN0QztRQUNJQyxXQUFXO1FBQ1hDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsY0FBYztJQUNsQjtJQUNBO1FBQ0lMLFdBQVc7UUFDWEMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxjQUFjO0lBQ2xCO0lBQ0E7UUFDSUwsV0FBVztRQUNYQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLGNBQWM7SUFDbEI7SUFDQTtRQUNJTCxXQUFXO1FBQ1hDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsY0FBYztJQUNsQjtDQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmFzdGNvZHItbmV4dC8uL2FwcC9hcGkvbW9kZWwvY29uZmlnLnRzP2U2YWMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gTW9kZWwgY29uZmlndXJhdGlvbiBmaWxlXHJcbi8vIENvbmZpZ3VyZSBtb2RlbHMgYmFzZWQgb24gYWN0dWFsIHNjZW5hcmlvc1xyXG5cclxuaW50ZXJmYWNlIE1vZGVsQ29uZmlnIHtcclxuICAgIG1vZGVsTmFtZTogc3RyaW5nO1xyXG4gICAgbW9kZWxLZXk6IHN0cmluZztcclxuICAgIHVzZUltYWdlOiBib29sZWFuO1xyXG4gICAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XHJcbiAgICBpY29uVXJsPzogc3RyaW5nO1xyXG4gICAgcHJvdmlkZXI/OiBzdHJpbmc7IC8vIE1vZGVsIHByb3ZpZGVyXHJcbiAgICBhcGlLZXk/OiBzdHJpbmc7XHJcbiAgICBhcGlVcmw/OiBzdHJpbmc7XHJcbiAgICBmdW5jdGlvbkNhbGw6IGJvb2xlYW47XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBtb2RlbENvbmZpZzogTW9kZWxDb25maWdbXSA9IFtcclxuICAgIHtcclxuICAgICAgICBtb2RlbE5hbWU6ICdjbGF1ZGUtMy01LXNvbm5ldCcsXHJcbiAgICAgICAgbW9kZWxLZXk6ICdjbGF1ZGUtMy01LXNvbm5ldC0yMDI0MDYyMCcsXHJcbiAgICAgICAgdXNlSW1hZ2U6IHRydWUsXHJcbiAgICAgICAgcHJvdmlkZXI6ICdjbGF1ZGUnLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnQ2xhdWRlIDMuNSBTb25uZXQgbW9kZWwnLFxyXG4gICAgICAgIGZ1bmN0aW9uQ2FsbDogdHJ1ZSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgICAgbW9kZWxOYW1lOiAnZ3B0LTRvLW1pbmknLFxyXG4gICAgICAgIG1vZGVsS2V5OiAnZ3B0LTRvLW1pbmknLFxyXG4gICAgICAgIHVzZUltYWdlOiBmYWxzZSxcclxuICAgICAgICBwcm92aWRlcjogJ29wZW5haScsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdHUFQtNCBPcHRpbWl6ZWQgTWluaSBtb2RlbCcsXHJcbiAgICAgICAgZnVuY3Rpb25DYWxsOiB0cnVlLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBtb2RlbE5hbWU6ICdkZWVwc2Vlay1SMScsXHJcbiAgICAgICAgbW9kZWxLZXk6ICdkZWVwc2Vlay1yZWFzb25lcicsXHJcbiAgICAgICAgdXNlSW1hZ2U6IGZhbHNlLFxyXG4gICAgICAgIHByb3ZpZGVyOiAnZGVlcHNlZWsnLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnRGVlcHNlZWsgUjEgbW9kZWwgd2l0aCByZWFzb25pbmcgYW5kIGNoYWluLW9mLXRob3VnaHQgY2FwYWJpbGl0aWVzJyxcclxuICAgICAgICBmdW5jdGlvbkNhbGw6IGZhbHNlLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBtb2RlbE5hbWU6ICdkZWVwc2Vlay12MycsXHJcbiAgICAgICAgbW9kZWxLZXk6ICdkZWVwc2Vlay1jaGF0JyxcclxuICAgICAgICB1c2VJbWFnZTogZmFsc2UsXHJcbiAgICAgICAgcHJvdmlkZXI6ICdkZWVwc2VlaycsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdEZWVwc2VlayBWMyBtb2RlbCcsXHJcbiAgICAgICAgZnVuY3Rpb25DYWxsOiB0cnVlLFxyXG4gICAgfVxyXG5dXHJcbiJdLCJuYW1lcyI6WyJtb2RlbENvbmZpZyIsIm1vZGVsTmFtZSIsIm1vZGVsS2V5IiwidXNlSW1hZ2UiLCJwcm92aWRlciIsImRlc2NyaXB0aW9uIiwiZnVuY3Rpb25DYWxsIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/api/model/config.ts\n");

/***/ }),

/***/ "(rsc)/./app/api/model/route.ts":
/*!********************************!*\
  !*** ./app/api/model/route.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(rsc)/./app/api/model/config.ts\");\n\n\n// 获取模型配置, 可以迁移到配置中心\nasync function POST() {\n    // 过滤掉key部分\n    const config = _config__WEBPACK_IMPORTED_MODULE_1__.modelConfig.map((item)=>{\n        return {\n            label: item.modelName,\n            value: item.modelKey,\n            useImage: item.useImage,\n            description: item.description,\n            icon: item.iconUrl,\n            provider: item.provider,\n            functionCall: item.functionCall\n        };\n    });\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(config);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL21vZGVsL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEyQztBQUNKO0FBQ3ZDLG9CQUFvQjtBQUNiLGVBQWVFO0lBQ2xCLFdBQVc7SUFDWCxNQUFNQyxTQUFTRixnREFBV0EsQ0FBQ0csR0FBRyxDQUFDQyxDQUFBQTtRQUMzQixPQUFPO1lBQ0hDLE9BQU9ELEtBQUtFLFNBQVM7WUFDckJDLE9BQU9ILEtBQUtJLFFBQVE7WUFDcEJDLFVBQVVMLEtBQUtLLFFBQVE7WUFDdkJDLGFBQWFOLEtBQUtNLFdBQVc7WUFDN0JDLE1BQU1QLEtBQUtRLE9BQU87WUFDbEJDLFVBQVVULEtBQUtTLFFBQVE7WUFDdkJDLGNBQWNWLEtBQUtVLFlBQVk7UUFDbkM7SUFDSjtJQUNDLE9BQU9mLHFEQUFZQSxDQUFDZ0IsSUFBSSxDQUFDYjtBQUM5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zhc3Rjb2RyLW5leHQvLi9hcHAvYXBpL21vZGVsL3JvdXRlLnRzPzA1NDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlc3BvbnNlIH0gZnJvbSBcIm5leHQvc2VydmVyXCI7XHJcbmltcG9ydCB7IG1vZGVsQ29uZmlnIH0gZnJvbSBcIi4vY29uZmlnXCI7XHJcbi8vIOiOt+WPluaooeWei+mFjee9riwg5Y+v5Lul6L+B56e75Yiw6YWN572u5Lit5b+DXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKCkge1xyXG4gICAgLy8g6L+H5ruk5o6Ja2V56YOo5YiGXHJcbiAgICBjb25zdCBjb25maWcgPSBtb2RlbENvbmZpZy5tYXAoaXRlbSA9PiB7XHJcbiAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgbGFiZWw6IGl0ZW0ubW9kZWxOYW1lLFxyXG4gICAgICAgICAgICB2YWx1ZTogaXRlbS5tb2RlbEtleSxcclxuICAgICAgICAgICAgdXNlSW1hZ2U6IGl0ZW0udXNlSW1hZ2UsXHJcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBpdGVtLmRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgICBpY29uOiBpdGVtLmljb25VcmwsXHJcbiAgICAgICAgICAgIHByb3ZpZGVyOiBpdGVtLnByb3ZpZGVyLFxyXG4gICAgICAgICAgICBmdW5jdGlvbkNhbGw6IGl0ZW0uZnVuY3Rpb25DYWxsLFxyXG4gICAgICAgIH1cclxuICAgIH0pXHJcbiAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKGNvbmZpZyk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsIm1vZGVsQ29uZmlnIiwiUE9TVCIsImNvbmZpZyIsIm1hcCIsIml0ZW0iLCJsYWJlbCIsIm1vZGVsTmFtZSIsInZhbHVlIiwibW9kZWxLZXkiLCJ1c2VJbWFnZSIsImRlc2NyaXB0aW9uIiwiaWNvbiIsImljb25VcmwiLCJwcm92aWRlciIsImZ1bmN0aW9uQ2FsbCIsImpzb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/api/model/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmodel%2Froute&page=%2Fapi%2Fmodel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodel%2Froute.ts&appDir=C%3A%5CUsers%5Cjay%20prakash%5COneDrive%5CDesktop%5Cjitender%5CJitender%20Works%5Cfastcodr%5Capps%5Cfastcodr-next%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5Cjay%20prakash%5COneDrive%5CDesktop%5Cjitender%5CJitender%20Works%5Cfastcodr%5Capps%5Cfastcodr-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();