import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { toast } from 'react-hot-toast';
import { UserRole } from '@/types/auth';

// Admin interfaces
export interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  status: 'active' | 'suspended' | 'pending';
  subscription: {
    plan: 'free' | 'premium' | 'enterprise';
    status: 'active' | 'cancelled' | 'expired';
    startDate: Date;
    endDate?: Date;
    amount: number;
  } | null;
  usage: {
    projects: number;
    aiCalls: number;
    storage: number;
    lastActive: Date;
  };
  createdAt: Date;
  lastLoginAt?: Date;
  loginCount: number;
  ipAddress?: string;
}

export interface DashboardStats {
  users: {
    total: number;
    active: number;
    new: number;
    subscribers: number;
    admins: number;
    growth: number;
  };
  projects: {
    total: number;
    public: number;
    private: number;
    templates: number;
    growth: number;
  };
  subscriptions: {
    active: number;
    revenue: number;
    churnRate: number;
    conversionRate: number;
    mrr: number;
    growth: number;
  };
  system: {
    uptime: number;
    responseTime: number;
    errorRate: number;
    storageUsed: number;
    storageLimit: number;
    apiCalls: number;
  };
}

export interface AnalyticsData {
  overview: {
    totalUsers: number;
    activeUsers: number;
    totalProjects: number;
    totalRevenue: number;
    conversionRate: number;
    churnRate: number;
  };
  userGrowth: {
    date: string;
    newUsers: number;
    activeUsers: number;
    churnedUsers: number;
  }[];
  revenueGrowth: {
    date: string;
    revenue: number;
    subscriptions: number;
    mrr: number;
  }[];
  featureUsage: {
    feature: string;
    usage: number;
    users: number;
    growth: number;
  }[];
  geographics: {
    country: string;
    users: number;
    revenue: number;
    percentage: number;
  }[];
  devices: {
    device: string;
    users: number;
    percentage: number;
  }[];
}

export interface SystemSettings {
  maintenance: {
    enabled: boolean;
    message: string;
    scheduledStart?: Date;
    scheduledEnd?: Date;
  };
  features: {
    aiAssistant: boolean;
    visualBuilder: boolean;
    projectSharing: boolean;
    templates: boolean;
    collaboration: boolean;
  };
  limits: {
    freeUserProjects: number;
    freeUserStorage: number;
    subscriberProjects: number;
    subscriberStorage: number;
    maxFileSize: number;
    maxProjectSize: number;
  };
  security: {
    passwordMinLength: number;
    requireEmailVerification: boolean;
    sessionTimeout: number;
    maxLoginAttempts: number;
    enableTwoFactor: boolean;
  };
  notifications: {
    emailNotifications: boolean;
    systemAlerts: boolean;
    maintenanceNotices: boolean;
    securityAlerts: boolean;
  };
}

interface AdminState {
  // Dashboard data
  dashboardStats: DashboardStats | null;
  analytics: AnalyticsData | null;
  
  // User management
  users: AdminUser[];
  userStats: {
    total: number;
    active: number;
    suspended: number;
    subscribers: number;
    admins: number;
  } | null;
  
  // System settings
  systemSettings: SystemSettings | null;
  
  // Loading states
  isLoadingDashboard: boolean;
  isLoadingUsers: boolean;
  isLoadingAnalytics: boolean;
  isLoadingSettings: boolean;
  
  // Actions
  loadDashboardStats: (timeRange?: string) => Promise<void>;
  loadUsers: (filters?: { role?: UserRole; status?: string; search?: string }) => Promise<void>;
  loadAnalytics: (timeRange?: string) => Promise<void>;
  loadSystemSettings: () => Promise<void>;
  
  updateUser: (userId: string, updates: Partial<AdminUser>) => Promise<void>;
  suspendUser: (userId: string) => Promise<void>;
  activateUser: (userId: string) => Promise<void>;
  deleteUser: (userId: string) => Promise<void>;
  
  updateSystemSettings: (settings: Partial<SystemSettings>) => Promise<void>;
  
  // Utility actions
  refreshAllData: () => Promise<void>;
  exportData: (type: 'users' | 'analytics' | 'projects') => Promise<void>;
}

const useAdminStore = create<AdminState>()(
  persist(
    (set, get) => ({
      // Initial state
      dashboardStats: null,
      analytics: null,
      users: [],
      userStats: null,
      systemSettings: null,
      isLoadingDashboard: false,
      isLoadingUsers: false,
      isLoadingAnalytics: false,
      isLoadingSettings: false,
      
      // Dashboard actions
      loadDashboardStats: async (timeRange = '30d') => {
        set({ isLoadingDashboard: true });
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/admin/dashboard?timeRange=${timeRange}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (!response.ok) {
            throw new Error('Failed to load dashboard stats');
          }
          
          const data = await response.json();
          set({ dashboardStats: data.data.stats });
        } catch (error) {
          console.error('Error loading dashboard stats:', error);
          toast.error('Failed to load dashboard data');
        } finally {
          set({ isLoadingDashboard: false });
        }
      },
      
      // User management actions
      loadUsers: async (filters = {}) => {
        set({ isLoadingUsers: true });
        try {
          const token = localStorage.getItem('token');
          const params = new URLSearchParams();
          
          if (filters.role) params.append('role', filters.role);
          if (filters.status) params.append('status', filters.status);
          if (filters.search) params.append('search', filters.search);
          
          const response = await fetch(`${process.env.APP_BASE_URL}/api/admin/users?${params}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (!response.ok) {
            throw new Error('Failed to load users');
          }
          
          const data = await response.json();
          set({ 
            users: data.data.users,
            userStats: data.data.stats
          });
        } catch (error) {
          console.error('Error loading users:', error);
          toast.error('Failed to load users');
        } finally {
          set({ isLoadingUsers: false });
        }
      },
      
      updateUser: async (userId, updates) => {
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/admin/users`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ userId, ...updates })
          });
          
          if (!response.ok) {
            throw new Error('Failed to update user');
          }
          
          // Update local state
          set(state => ({
            users: state.users.map(user => 
              user.id === userId ? { ...user, ...updates } : user
            )
          }));
          
          toast.success('User updated successfully');
        } catch (error) {
          console.error('Error updating user:', error);
          toast.error('Failed to update user');
        }
      },
      
      suspendUser: async (userId) => {
        await get().updateUser(userId, { status: 'suspended' });
      },
      
      activateUser: async (userId) => {
        await get().updateUser(userId, { status: 'active' });
      },
      
      deleteUser: async (userId) => {
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/admin/users?userId=${userId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (!response.ok) {
            throw new Error('Failed to delete user');
          }
          
          // Remove from local state
          set(state => ({
            users: state.users.filter(user => user.id !== userId)
          }));
          
          toast.success('User deleted successfully');
        } catch (error) {
          console.error('Error deleting user:', error);
          toast.error('Failed to delete user');
        }
      },
      
      // Analytics actions
      loadAnalytics: async (timeRange = '30d') => {
        set({ isLoadingAnalytics: true });
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/admin/analytics?timeRange=${timeRange}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (!response.ok) {
            throw new Error('Failed to load analytics');
          }
          
          const data = await response.json();
          set({ analytics: data.data });
        } catch (error) {
          console.error('Error loading analytics:', error);
          toast.error('Failed to load analytics data');
        } finally {
          set({ isLoadingAnalytics: false });
        }
      },
      
      // System settings actions
      loadSystemSettings: async () => {
        set({ isLoadingSettings: true });
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/admin/settings`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (!response.ok) {
            throw new Error('Failed to load system settings');
          }
          
          const data = await response.json();
          set({ systemSettings: data.data });
        } catch (error) {
          console.error('Error loading system settings:', error);
          toast.error('Failed to load system settings');
        } finally {
          set({ isLoadingSettings: false });
        }
      },
      
      updateSystemSettings: async (settings) => {
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/admin/settings`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
          });
          
          if (!response.ok) {
            throw new Error('Failed to update system settings');
          }
          
          // Update local state
          set(state => ({
            systemSettings: state.systemSettings ? { ...state.systemSettings, ...settings } : null
          }));
          
          toast.success('System settings updated successfully');
        } catch (error) {
          console.error('Error updating system settings:', error);
          toast.error('Failed to update system settings');
        }
      },
      
      // Utility actions
      refreshAllData: async () => {
        const { loadDashboardStats, loadUsers, loadAnalytics, loadSystemSettings } = get();
        await Promise.all([
          loadDashboardStats(),
          loadUsers(),
          loadAnalytics(),
          loadSystemSettings()
        ]);
      },
      
      exportData: async (type) => {
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${process.env.APP_BASE_URL}/api/admin/export/${type}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (!response.ok) {
            throw new Error(`Failed to export ${type} data`);
          }
          
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `fastcodr-${type}-export-${new Date().toISOString().split('T')[0]}.csv`;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
          
          toast.success(`${type} data exported successfully`);
        } catch (error) {
          console.error(`Error exporting ${type} data:`, error);
          toast.error(`Failed to export ${type} data`);
        }
      }
    }),
    {
      name: 'fastcodr-admin-store',
      partialize: (state) => ({
        // Only persist non-sensitive data
        systemSettings: state.systemSettings
      })
    }
  )
);

export default useAdminStore;
