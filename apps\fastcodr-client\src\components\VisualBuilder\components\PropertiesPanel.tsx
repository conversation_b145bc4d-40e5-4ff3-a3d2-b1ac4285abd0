import React, { useState } from 'react';
import { Settings, Trash2, Palette, Layout, Type, Image } from 'lucide-react';
import { UIComponent } from '../VisualBuilder';

interface PropertiesPanelProps {
  component: UIComponent | null | undefined;
  onUpdate: (updates: Partial<UIComponent>) => void;
  onRemove: () => void;
}

const PropertiesPanel: React.FC<PropertiesPanelProps> = ({ component, onUpdate, onRemove }) => {
  const [activeTab, setActiveTab] = useState<'props' | 'style'>('props');

  if (!component) return null;

  const updateProp = (key: string, value: any) => {
    onUpdate({
      props: { ...component.props, [key]: value }
    });
  };

  const updateStyle = (key: string, value: any) => {
    onUpdate({
      style: { ...component.style, [key]: value }
    });
  };

  const commonStyleProps = [
    { key: 'width', label: 'Width', type: 'text', placeholder: 'auto, 100px, 50%' },
    { key: 'height', label: 'Height', type: 'text', placeholder: 'auto, 100px, 50%' },
    { key: 'margin', label: 'Margin', type: 'text', placeholder: '0, 10px, 1rem' },
    { key: 'padding', label: 'Padding', type: 'text', placeholder: '0, 10px, 1rem' },
    { key: 'backgroundColor', label: 'Background', type: 'color' },
    { key: 'color', label: 'Text Color', type: 'color' },
    { key: 'fontSize', label: 'Font Size', type: 'text', placeholder: '14px, 1rem' },
    { key: 'fontWeight', label: 'Font Weight', type: 'select', options: ['normal', 'bold', '100', '200', '300', '400', '500', '600', '700', '800', '900'] },
    { key: 'textAlign', label: 'Text Align', type: 'select', options: ['left', 'center', 'right', 'justify'] },
    { key: 'border', label: 'Border', type: 'text', placeholder: '1px solid #ccc' },
    { key: 'borderRadius', label: 'Border Radius', type: 'text', placeholder: '0, 4px, 50%' },
    { key: 'display', label: 'Display', type: 'select', options: ['block', 'inline', 'inline-block', 'flex', 'grid', 'none'] },
    { key: 'position', label: 'Position', type: 'select', options: ['static', 'relative', 'absolute', 'fixed', 'sticky'] },
  ];

  const getComponentSpecificProps = () => {
    switch (component.type) {
      case 'img':
        return [
          { key: 'src', label: 'Image URL', type: 'text' },
          { key: 'alt', label: 'Alt Text', type: 'text' },
        ];
      case 'input':
        return [
          { key: 'type', label: 'Input Type', type: 'select', options: ['text', 'email', 'password', 'number', 'tel', 'url'] },
          { key: 'placeholder', label: 'Placeholder', type: 'text' },
          { key: 'value', label: 'Value', type: 'text' },
          { key: 'disabled', label: 'Disabled', type: 'checkbox' },
          { key: 'required', label: 'Required', type: 'checkbox' },
        ];
      case 'button':
        return [
          { key: 'type', label: 'Button Type', type: 'select', options: ['button', 'submit', 'reset'] },
          { key: 'disabled', label: 'Disabled', type: 'checkbox' },
        ];
      case 'a':
        return [
          { key: 'href', label: 'Link URL', type: 'text' },
          { key: 'target', label: 'Target', type: 'select', options: ['_self', '_blank', '_parent', '_top'] },
        ];
      default:
        return [];
    }
  };

  const renderPropInput = (prop: any) => {
    const value = component.props[prop.key] || '';

    switch (prop.type) {
      case 'checkbox':
        return (
          <input
            type="checkbox"
            checked={!!value}
            onChange={(e) => updateProp(prop.key, e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
        );
      
      case 'select':
        return (
          <select
            value={value}
            onChange={(e) => updateProp(prop.key, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select...</option>
            {prop.options?.map((option: string) => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        );
      
      case 'color':
        return (
          <div className="flex gap-2">
            <input
              type="color"
              value={value || '#000000'}
              onChange={(e) => updateProp(prop.key, e.target.value)}
              className="w-12 h-8 border border-gray-300 rounded cursor-pointer"
            />
            <input
              type="text"
              value={value}
              onChange={(e) => updateProp(prop.key, e.target.value)}
              placeholder="#000000"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        );
      
      default:
        return (
          <input
            type="text"
            value={value}
            onChange={(e) => updateProp(prop.key, e.target.value)}
            placeholder={prop.placeholder}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        );
    }
  };

  const renderStyleInput = (prop: any) => {
    const value = component.style?.[prop.key] || '';

    switch (prop.type) {
      case 'select':
        return (
          <select
            value={value}
            onChange={(e) => updateStyle(prop.key, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Default</option>
            {prop.options?.map((option: string) => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        );
      
      case 'color':
        return (
          <div className="flex gap-2">
            <input
              type="color"
              value={value || '#000000'}
              onChange={(e) => updateStyle(prop.key, e.target.value)}
              className="w-12 h-8 border border-gray-300 rounded cursor-pointer"
            />
            <input
              type="text"
              value={value}
              onChange={(e) => updateStyle(prop.key, e.target.value)}
              placeholder={prop.placeholder || '#000000'}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        );
      
      default:
        return (
          <input
            type="text"
            value={value}
            onChange={(e) => updateStyle(prop.key, e.target.value)}
            placeholder={prop.placeholder}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        );
    }
  };

  return (
    <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-gray-900 flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Properties
          </h3>
          <button
            onClick={onRemove}
            className="p-1 text-red-500 hover:bg-red-50 rounded transition-colors"
            title="Remove component"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
        
        <div className="text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded">
          <span className="font-medium">{component.type}</span>
          <span className="text-gray-400 ml-2">#{component.id.slice(-8)}</span>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200">
        <button
          onClick={() => setActiveTab('props')}
          className={`flex-1 px-4 py-2 text-sm font-medium ${
            activeTab === 'props'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <Type className="w-4 h-4 inline mr-2" />
          Properties
        </button>
        <button
          onClick={() => setActiveTab('style')}
          className={`flex-1 px-4 py-2 text-sm font-medium ${
            activeTab === 'style'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <Palette className="w-4 h-4 inline mr-2" />
          Styling
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {activeTab === 'props' ? (
          <div className="space-y-4">
            {/* Text Content */}
            {['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span', 'button'].includes(component.type) && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Text Content
                </label>
                <textarea
                  value={component.props.children || ''}
                  onChange={(e) => updateProp('children', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                />
              </div>
            )}

            {/* CSS Classes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                CSS Classes
              </label>
              <input
                type="text"
                value={component.props.className || ''}
                onChange={(e) => updateProp('className', e.target.value)}
                placeholder="Enter CSS classes"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Component-specific props */}
            {getComponentSpecificProps().map((prop) => (
              <div key={prop.key}>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {prop.label}
                </label>
                {renderPropInput(prop)}
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-4">
            {commonStyleProps.map((prop) => (
              <div key={prop.key}>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {prop.label}
                </label>
                {renderStyleInput(prop)}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default PropertiesPanel;
