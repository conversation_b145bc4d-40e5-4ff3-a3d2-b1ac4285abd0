{
  "productName": "FastCodr",
  "appId": "com.fastcodr.app",
  "afterSign": null,
  "directories": {
    "output": "release"
  },
  "files": [
    "dist/**/*",
    "dist-electron/**/*"
  ],
  "mac": {
    "icon": "config/icon-512.png",
    "target": [
      {
        "target": "dmg",
        "arch": ["arm64"]
      }
    ],
    "category": "public.app-category.developer-tools",
    "hardenedRuntime": true,
    "gatekeeperAssess": false,
  },
  "dmg": {
    "sign": false,
    "contents": [
      {
        "x": 130,
        "y": 220
      },
      {
        "x": 410,
        "y": 220,
        "type": "link",
        "path": "/Applications"
      }
    ]
  },
  "win": {
    "icon": "config/icon-512.png",
    "target": [
      "nsis",
      "zip"
    ]
  },
  "extraResources": [
    {
      "from": "/bin/zsh",
      "to": "bin/zsh"
    },
    {
      "from": "/usr/local/bin/npm",
      "to": "bin/npm"
    }
  ],
  "asar": true,
  "asarUnpack": [
    "dist-electron",
    "dist/electron",
    "node_modules/node-pty/**/*"
  ]
}
