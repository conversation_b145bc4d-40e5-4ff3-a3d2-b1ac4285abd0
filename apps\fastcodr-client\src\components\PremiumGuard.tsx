import React from 'react';
import useSubscriptionStore from '@/stores/subscriptionSlice';
import { Crown, Lock, Zap } from 'lucide-react';

interface PremiumGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showUpgradePrompt?: boolean;
  featureName?: string;
}

export function PremiumGuard({ 
  children, 
  fallback, 
  showUpgradePrompt = true, 
  featureName = "this feature" 
}: PremiumGuardProps) {
  const { isSubscribed } = useSubscriptionStore();

  if (isSubscribed) {
    return <>{children}</>;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  if (showUpgradePrompt) {
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
        <div className="flex items-center gap-2 mb-4">
          <Crown className="w-8 h-8 text-yellow-500" />
          <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
            Premium Feature
          </h3>
        </div>
        
        <p className="text-gray-600 dark:text-gray-400 text-center mb-6 max-w-md">
          Unlock {featureName} and all premium features with FastCodr Premium.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={() => window.location.href = '/subscribe'}
            className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            <Zap className="w-4 h-4" />
            Upgrade to Premium
          </button>
          
          <button
            onClick={() => window.history.back()}
            className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return null;
}

// Hook to check if user has premium access
export function usePremiumAccess() {
  const { isSubscribed } = useSubscriptionStore();
  return isSubscribed;
}

// Component to show premium badge
export function PremiumBadge({ className = "" }: { className?: string }) {
  return (
    <span className={`inline-flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-semibold rounded-full ${className}`}>
      <Crown className="w-3 h-3" />
      PRO
    </span>
  );
}

// Component for premium feature buttons
export function PremiumButton({ 
  children, 
  onClick, 
  disabled = false,
  className = "",
  ...props 
}: React.ButtonHTMLAttributes<HTMLButtonElement>) {
  const { isSubscribed } = useSubscriptionStore();

  if (!isSubscribed) {
    return (
      <button
        onClick={() => window.location.href = '/subscribe'}
        className={`relative flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 ${className}`}
        {...props}
      >
        <Lock className="w-4 h-4" />
        {children}
        <PremiumBadge className="ml-2" />
      </button>
    );
  }

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={className}
      {...props}
    >
      {children}
    </button>
  );
}
