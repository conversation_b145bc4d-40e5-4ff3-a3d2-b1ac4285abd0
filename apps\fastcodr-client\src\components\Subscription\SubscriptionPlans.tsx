import React, { useState, useEffect } from 'react';
import {
  Check,
  Crown,
  Zap,
  Users,
  Shield,
  Star,
  ArrowRight,
  <PERSON>rk<PERSON>,
  Clock,
  CreditCard
} from 'lucide-react';
import useUserStore from '@/stores/userSlice';
import { toast } from 'react-hot-toast';

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'monthly' | 'yearly';
  features: string[];
  limits: {
    projects: number;
    aiCalls: number;
    storage: number;
    collaborators: number;
    templates: number;
    exportFormats: string[];
  };
  isPopular: boolean;
  isActive: boolean;
  trialDays?: number;
}

interface CurrentSubscription {
  id: string;
  planId: string;
  status: string;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
}

const SubscriptionPlans: React.FC = () => {
  const { user, token } = useUserStore();
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [currentSubscription, setCurrentSubscription] = useState<CurrentSubscription | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [billingInterval, setBillingInterval] = useState<'monthly' | 'yearly'>('monthly');

  useEffect(() => {
    fetchSubscriptionData();
  }, []);

  const fetchSubscriptionData = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`${process.env.APP_BASE_URL}/api/subscriptions`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch subscription data');
      }

      const data = await response.json();
      setPlans(data.data.availablePlans);
      setCurrentSubscription(data.data.currentSubscription);
    } catch (error) {
      console.error('Error fetching subscription data:', error);
      toast.error('Failed to load subscription plans');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubscribe = async (planId: string) => {
    if (!user) {
      toast.error('Please log in to subscribe');
      return;
    }

    setIsProcessing(true);
    setSelectedPlan(planId);

    try {
      const response = await fetch(`${process.env.APP_BASE_URL}/api/subscriptions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ planId })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create subscription');
      }

      const data = await response.json();

      // Redirect to Cashfree payment page
      if (data.data.paymentUrl) {
        window.location.href = data.data.paymentUrl;
      } else {
        toast.success('Subscription created successfully');
        fetchSubscriptionData();
      }

    } catch (error) {
      console.error('Error creating subscription:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create subscription');
    } finally {
      setIsProcessing(false);
      setSelectedPlan(null);
    }
  };

  const formatPrice = (price: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(price);
  };

  const formatLimit = (limit: number) => {
    if (limit === -1) return 'Unlimited';
    if (limit >= 1000) return `${(limit / 1000).toFixed(0)}K`;
    return limit.toString();
  };

  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'free':
        return <Zap className="w-6 h-6 text-blue-500" />;
      case 'premium_monthly':
      case 'premium_yearly':
        return <Crown className="w-6 h-6 text-purple-500" />;
      case 'enterprise':
        return <Shield className="w-6 h-6 text-orange-500" />;
      default:
        return <Star className="w-6 h-6 text-gray-500" />;
    }
  };

  const isCurrentPlan = (planId: string) => {
    return currentSubscription?.planId === planId;
  };

  const filteredPlans = plans.filter(plan =>
    plan.interval === billingInterval || plan.id === 'free'
  );

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading subscription plans...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Choose Your Plan
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 mb-8">
            Unlock the full potential of FastCodr with our premium features
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center mb-8">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-1 border border-gray-200 dark:border-gray-700">
              <button
                onClick={() => setBillingInterval('monthly')}
                className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
                  billingInterval === 'monthly'
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                Monthly
              </button>
              <button
                onClick={() => setBillingInterval('yearly')}
                className={`px-6 py-2 rounded-md text-sm font-medium transition-colors relative ${
                  billingInterval === 'yearly'
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                Yearly
                <span className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                  Save 20%
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* Current Subscription Status */}
        {currentSubscription && (
          <div className="mb-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
            <div className="flex items-center gap-3">
              <Crown className="w-6 h-6 text-blue-600" />
              <div>
                <h3 className="font-semibold text-blue-900 dark:text-blue-100">
                  Current Subscription
                </h3>
                <p className="text-blue-700 dark:text-blue-300">
                  You're currently on the {plans.find(p => p.id === currentSubscription.planId)?.name} plan
                  {currentSubscription.cancelAtPeriodEnd && (
                    <span className="ml-2 text-orange-600">
                      (Cancels on {new Date(currentSubscription.currentPeriodEnd).toLocaleDateString()})
                    </span>
                  )}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredPlans.map((plan) => (
            <PlanCard
              key={plan.id}
              plan={plan}
              isCurrentPlan={isCurrentPlan(plan.id)}
              isProcessing={isProcessing && selectedPlan === plan.id}
              onSubscribe={() => handleSubscribe(plan.id)}
              formatPrice={formatPrice}
              formatLimit={formatLimit}
              getPlanIcon={getPlanIcon}
            />
          ))}
        </div>

        {/* Features Comparison */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white text-center mb-8">
            Feature Comparison
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
            <FeatureComparison plans={filteredPlans} />
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white text-center mb-8">
            Frequently Asked Questions
          </h2>
          <div className="max-w-3xl mx-auto">
            <FAQSection />
          </div>
        </div>
      </div>
    </div>
  );
};

interface PlanCardProps {
  plan: SubscriptionPlan;
  isCurrentPlan: boolean;
  isProcessing: boolean;
  onSubscribe: () => void;
  formatPrice: (price: number, currency?: string) => string;
  formatLimit: (limit: number) => string;
  getPlanIcon: (planId: string) => React.ReactNode;
}

const PlanCard: React.FC<PlanCardProps> = ({
  plan,
  isCurrentPlan,
  isProcessing,
  onSubscribe,
  formatPrice,
  formatLimit,
  getPlanIcon
}) => {
  const isFree = plan.price === 0;
  const isPopular = plan.isPopular;

  return (
    <div className={`relative bg-white dark:bg-gray-800 rounded-lg border-2 transition-all duration-200 ${
      isPopular
        ? 'border-blue-500 shadow-lg scale-105'
        : 'border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600'
    }`}>
      {/* Popular Badge */}
      {isPopular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <div className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center gap-1">
            <Sparkles className="w-4 h-4" />
            Most Popular
          </div>
        </div>
      )}

      <div className="p-6">
        {/* Plan Header */}
        <div className="text-center mb-6">
          <div className="flex justify-center mb-3">
            {getPlanIcon(plan.id)}
          </div>
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            {plan.name}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            {plan.description}
          </p>
        </div>

        {/* Pricing */}
        <div className="text-center mb-6">
          <div className="flex items-baseline justify-center">
            <span className="text-4xl font-bold text-gray-900 dark:text-white">
              {isFree ? 'Free' : formatPrice(plan.price, plan.currency)}
            </span>
            {!isFree && (
              <span className="text-gray-600 dark:text-gray-400 ml-2">
                /{plan.interval}
              </span>
            )}
          </div>
          {plan.trialDays && (
            <div className="flex items-center justify-center gap-1 mt-2 text-green-600 text-sm">
              <Clock className="w-4 h-4" />
              {plan.trialDays} days free trial
            </div>
          )}
        </div>

        {/* Features */}
        <div className="space-y-3 mb-6">
          {plan.features.map((feature, index) => (
            <div key={index} className="flex items-center gap-3">
              <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
              <span className="text-gray-700 dark:text-gray-300 text-sm">
                {feature}
              </span>
            </div>
          ))}
        </div>

        {/* Limits */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div>
              <span className="text-gray-600 dark:text-gray-400">Projects:</span>
              <span className="font-medium text-gray-900 dark:text-white ml-1">
                {formatLimit(plan.limits.projects)}
              </span>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">AI Calls:</span>
              <span className="font-medium text-gray-900 dark:text-white ml-1">
                {formatLimit(plan.limits.aiCalls)}
              </span>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Storage:</span>
              <span className="font-medium text-gray-900 dark:text-white ml-1">
                {formatLimit(plan.limits.storage)}MB
              </span>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Team:</span>
              <span className="font-medium text-gray-900 dark:text-white ml-1">
                {formatLimit(plan.limits.collaborators)}
              </span>
            </div>
          </div>
        </div>

        {/* Action Button */}
        <button
          onClick={onSubscribe}
          disabled={isCurrentPlan || isProcessing}
          className={`w-full py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2 ${
            isCurrentPlan
              ? 'bg-gray-100 dark:bg-gray-700 text-gray-500 cursor-not-allowed'
              : isFree
              ? 'bg-gray-900 dark:bg-white text-white dark:text-gray-900 hover:bg-gray-800 dark:hover:bg-gray-100'
              : 'bg-blue-500 text-white hover:bg-blue-600'
          }`}
        >
          {isProcessing ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
              Processing...
            </>
          ) : isCurrentPlan ? (
            'Current Plan'
          ) : isFree ? (
            'Get Started'
          ) : (
            <>
              <CreditCard className="w-4 h-4" />
              Subscribe Now
            </>
          )}
        </button>
      </div>
    </div>
  );
};

const FeatureComparison: React.FC<{ plans: SubscriptionPlan[] }> = ({ plans }) => {
  const features = [
    { name: 'Code Editor', free: true, premium: true, enterprise: true },
    { name: 'Project Saving', free: true, premium: true, enterprise: true },
    { name: 'AI Assistant', free: false, premium: true, enterprise: true },
    { name: 'Visual Builder', free: false, premium: true, enterprise: true },
    { name: 'Premium Templates', free: false, premium: true, enterprise: true },
    { name: 'Real-time Collaboration', free: false, premium: true, enterprise: true },
    { name: 'Advanced Export', free: false, premium: true, enterprise: true },
    { name: 'Priority Support', free: false, premium: true, enterprise: true },
    { name: 'Custom Integrations', free: false, premium: false, enterprise: true },
    { name: 'SSO Integration', free: false, premium: false, enterprise: true },
    { name: 'Dedicated Support', free: false, premium: false, enterprise: true }
  ];

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead className="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Feature
            </th>
            {plans.map((plan) => (
              <th key={plan.id} className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                {plan.name}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
          {features.map((feature, index) => (
            <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                {feature.name}
              </td>
              {plans.map((plan) => {
                const hasFeature = plan.features.some(f => f.toLowerCase().includes(feature.name.toLowerCase())) ||
                  (feature.name === 'Code Editor' || feature.name === 'Project Saving');

                return (
                  <td key={plan.id} className="px-6 py-4 whitespace-nowrap text-center">
                    {hasFeature ? (
                      <Check className="w-5 h-5 text-green-500 mx-auto" />
                    ) : (
                      <span className="text-gray-400">—</span>
                    )}
                  </td>
                );
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

const FAQSection: React.FC = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const faqs = [
    {
      question: 'Can I cancel my subscription anytime?',
      answer: 'Yes, you can cancel your subscription at any time. Your subscription will remain active until the end of your current billing period.'
    },
    {
      question: 'Do you offer refunds?',
      answer: 'We offer a 30-day money-back guarantee for all paid plans. If you\'re not satisfied, contact our support team for a full refund.'
    },
    {
      question: 'What payment methods do you accept?',
      answer: 'We accept all major credit cards, debit cards, UPI, net banking, and digital wallets through our secure payment partner Cashfree.'
    },
    {
      question: 'Can I upgrade or downgrade my plan?',
      answer: 'Yes, you can change your plan at any time. Upgrades take effect immediately, while downgrades take effect at the next billing cycle.'
    },
    {
      question: 'Is there a free trial?',
      answer: 'Yes! Premium plans come with a 7-day free trial (14 days for yearly plans). No credit card required to start your trial.'
    },
    {
      question: 'What happens to my projects if I cancel?',
      answer: 'Your projects remain accessible even after cancellation. However, premium features will be disabled and you\'ll be limited to the free plan restrictions.'
    }
  ];

  return (
    <div className="space-y-4">
      {faqs.map((faq, index) => (
        <div key={index} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <button
            onClick={() => setOpenIndex(openIndex === index ? null : index)}
            className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <span className="font-medium text-gray-900 dark:text-white">
              {faq.question}
            </span>
            <ArrowRight className={`w-5 h-5 text-gray-500 transition-transform ${
              openIndex === index ? 'rotate-90' : ''
            }`} />
          </button>
          {openIndex === index && (
            <div className="px-6 pb-4">
              <p className="text-gray-600 dark:text-gray-400">
                {faq.answer}
              </p>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default SubscriptionPlans;
