import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, UserRole, Permission } from '@/lib/auth';
import { 
  Project, 
  UpdateProjectRequest,
  calculateProjectSize,
  countProjectFiles
} from '@/lib/models/Project';

// GET /api/projects/[id] - Get a specific project
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authResult = await requireAuth(UserRole.USER, [Permission.SAVE_PROJECT])(request);
    
    if (authResult instanceof Response) {
      return authResult;
    }

    const { user } = authResult;
    const projectId = params.id;

    // Mock project data - replace with database query
    const mockProject: Project = {
      id: projectId,
      name: 'My React App',
      description: 'A beautiful React application built with FastCodr',
      userId: user.userId,
      files: [
        {
          id: 'file_1',
          name: 'App.tsx',
          content: `import React, { useState } from 'react';
import './App.css';

function App() {
  const [count, setCount] = useState(0);

  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to FastCodr</h1>
        <p>You clicked {count} times</p>
        <button onClick={() => setCount(count + 1)}>
          Click me
        </button>
      </header>
    </div>
  );
}

export default App;`,
          language: 'typescript',
          path: '/App.tsx',
          size: 0,
          lastModified: new Date(),
          isDirectory: false
        },
        {
          id: 'file_2',
          name: 'index.tsx',
          content: `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './index.css';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`,
          language: 'typescript',
          path: '/index.tsx',
          size: 0,
          lastModified: new Date(),
          isDirectory: false
        },
        {
          id: 'file_3',
          name: 'App.css',
          content: `.App {
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

button {
  background-color: #1e90ff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  margin-top: 10px;
}

button:hover {
  background-color: #0066cc;
}`,
          language: 'css',
          path: '/App.css',
          size: 0,
          lastModified: new Date(),
          isDirectory: false
        }
      ],
      settings: {
        theme: 'dark',
        fontSize: 14,
        tabSize: 2,
        wordWrap: true,
        minimap: true,
        autoSave: true,
        preferredEditor: 'monaco'
      },
      metadata: {
        framework: 'react',
        language: 'typescript',
        template: 'react-typescript',
        dependencies: ['react', 'react-dom', '@types/react', '@types/react-dom'],
        buildCommand: 'npm run build',
        startCommand: 'npm start'
      },
      tags: ['react', 'typescript', 'frontend'],
      isPublic: false,
      isTemplate: false,
      collaborators: [],
      viewCount: 15,
      forkCount: 2,
      starCount: 5,
      status: 'published',
      version: '1.0.0',
      isDraft: false,
      totalSize: 0,
      fileCount: 0,
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-20'),
      lastOpenedAt: new Date()
    };

    // Calculate actual sizes
    mockProject.files.forEach(file => {
      file.size = file.content.length;
    });
    mockProject.totalSize = calculateProjectSize(mockProject.files);
    mockProject.fileCount = countProjectFiles(mockProject.files);

    // Check if user has access to this project
    if (mockProject.userId !== user.userId && !mockProject.isPublic) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      );
    }

    // Update last opened timestamp
    mockProject.lastOpenedAt = new Date();
    
    // TODO: Update in database
    // await updateProjectLastOpened(projectId, new Date());

    return NextResponse.json({
      success: true,
      data: mockProject
    });

  } catch (error) {
    console.error('Error fetching project:', error);
    return NextResponse.json(
      { error: 'Failed to fetch project' },
      { status: 500 }
    );
  }
}

// PUT /api/projects/[id] - Update a project
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authResult = await requireAuth(UserRole.USER, [Permission.SAVE_PROJECT])(request);
    
    if (authResult instanceof Response) {
      return authResult;
    }

    const { user } = authResult;
    const projectId = params.id;
    const updateRequest: UpdateProjectRequest = await request.json();

    // TODO: Fetch existing project from database
    // const existingProject = await getProjectById(projectId);
    
    // Mock existing project check
    const existingProject = { userId: user.userId }; // Simplified for demo

    if (!existingProject || existingProject.userId !== user.userId) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: Partial<Project> = {
      ...updateRequest,
      updatedAt: new Date()
    };

    // Calculate new sizes if files are updated
    if (updateRequest.files) {
      updateData.totalSize = calculateProjectSize(updateRequest.files);
      updateData.fileCount = countProjectFiles(updateRequest.files);
      
      // Update file sizes
      updateRequest.files.forEach(file => {
        file.size = file.content.length;
        file.lastModified = new Date();
      });
    }

    // Auto-save logic
    if (updateRequest.files && !updateRequest.status) {
      updateData.isDraft = true;
      updateData.draftSavedAt = new Date();
    }

    // TODO: Update in database
    // const updatedProject = await updateProjectInDatabase(projectId, updateData);

    // Mock updated project
    const updatedProject = {
      id: projectId,
      ...updateData,
      userId: user.userId
    };

    return NextResponse.json({
      success: true,
      data: updatedProject,
      message: 'Project updated successfully'
    });

  } catch (error) {
    console.error('Error updating project:', error);
    return NextResponse.json(
      { error: 'Failed to update project' },
      { status: 500 }
    );
  }
}

// DELETE /api/projects/[id] - Delete a project
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authResult = await requireAuth(UserRole.USER, [Permission.SAVE_PROJECT])(request);
    
    if (authResult instanceof Response) {
      return authResult;
    }

    const { user } = authResult;
    const projectId = params.id;

    // TODO: Fetch existing project from database
    // const existingProject = await getProjectById(projectId);
    
    // Mock existing project check
    const existingProject = { userId: user.userId }; // Simplified for demo

    if (!existingProject || existingProject.userId !== user.userId) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      );
    }

    // TODO: Delete from database
    // await deleteProjectFromDatabase(projectId);

    // TODO: Clean up associated files and shares
    // await cleanupProjectFiles(projectId);
    // await deleteProjectShares(projectId);

    return NextResponse.json({
      success: true,
      message: 'Project deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting project:', error);
    return NextResponse.json(
      { error: 'Failed to delete project' },
      { status: 500 }
    );
  }
}
