// Authentication and authorization types for FastCodr

export enum UserRole {
  USER = "user",
  SUBSCRIBER = "subscriber", 
  ADMIN = "admin"
}

export enum Permission {
  // Basic permissions
  VIEW_EDITOR = "view_editor",
  SAVE_PROJECT = "save_project",
  
  // Subscriber permissions
  AI_ASSISTANT = "ai_assistant",
  UNLIMITED_PROJECTS = "unlimited_projects",
  PREMIUM_TEMPLATES = "premium_templates",
  EXPORT_PROJECTS = "export_projects",
  
  // Admin permissions
  ADMIN_DASHBOARD = "admin_dashboard",
  MANAGE_USERS = "manage_users",
  VIEW_ANALYTICS = "view_analytics",
  MANAGE_SUBSCRIPTIONS = "manage_subscriptions"
}

export interface RolePermissions {
  [UserRole.USER]: Permission[];
  [UserRole.SUBSCRIBER]: Permission[];
  [UserRole.ADMIN]: Permission[];
}

// Define permissions for each role
export const ROLE_PERMISSIONS: RolePermissions = {
  [UserRole.USER]: [
    Permission.VIEW_EDITOR,
    Permission.SAVE_PROJECT
  ],
  [UserRole.SUBSCRIBER]: [
    Permission.VIEW_EDITOR,
    Permission.SAVE_PROJECT,
    Permission.AI_ASSISTANT,
    Permission.UNLIMITED_PROJECTS,
    Permission.PREMIUM_TEMPLATES,
    Permission.EXPORT_PROJECTS
  ],
  [UserRole.ADMIN]: [
    Permission.VIEW_EDITOR,
    Permission.SAVE_PROJECT,
    Permission.AI_ASSISTANT,
    Permission.UNLIMITED_PROJECTS,
    Permission.PREMIUM_TEMPLATES,
    Permission.EXPORT_PROJECTS,
    Permission.ADMIN_DASHBOARD,
    Permission.MANAGE_USERS,
    Permission.VIEW_ANALYTICS,
    Permission.MANAGE_SUBSCRIPTIONS
  ]
};

// Usage limits per role
export interface UsageLimits {
  projectsPerMonth: number;
  aiCallsPerMonth: number;
  storageLimit: number; // in MB
  collaborators: number;
}

export const ROLE_LIMITS: Record<UserRole, UsageLimits> = {
  [UserRole.USER]: {
    projectsPerMonth: 3,
    aiCallsPerMonth: 50,
    storageLimit: 100,
    collaborators: 0
  },
  [UserRole.SUBSCRIBER]: {
    projectsPerMonth: 50,
    aiCallsPerMonth: 1000,
    storageLimit: 1000,
    collaborators: 5
  },
  [UserRole.ADMIN]: {
    projectsPerMonth: -1, // unlimited
    aiCallsPerMonth: -1, // unlimited
    storageLimit: -1, // unlimited
    collaborators: -1 // unlimited
  }
};

// Route access configuration
export interface RouteAccess {
  path: string;
  requiredRole: UserRole;
  requiredPermissions?: Permission[];
}

export const PROTECTED_ROUTES: RouteAccess[] = [
  {
    path: "/admin",
    requiredRole: UserRole.ADMIN,
    requiredPermissions: [Permission.ADMIN_DASHBOARD]
  },
  {
    path: "/admin/users",
    requiredRole: UserRole.ADMIN,
    requiredPermissions: [Permission.MANAGE_USERS]
  },
  {
    path: "/admin/analytics",
    requiredRole: UserRole.ADMIN,
    requiredPermissions: [Permission.VIEW_ANALYTICS]
  },
  {
    path: "/projects",
    requiredRole: UserRole.USER,
    requiredPermissions: [Permission.SAVE_PROJECT]
  }
];

// Helper functions
export const hasPermission = (userRole: UserRole, permission: Permission): boolean => {
  return ROLE_PERMISSIONS[userRole]?.includes(permission) || false;
};

export const hasAnyPermission = (userRole: UserRole, permissions: Permission[]): boolean => {
  return permissions.some(permission => hasPermission(userRole, permission));
};

export const hasAllPermissions = (userRole: UserRole, permissions: Permission[]): boolean => {
  return permissions.every(permission => hasPermission(userRole, permission));
};

export const canAccessRoute = (userRole: UserRole, route: string): boolean => {
  const routeConfig = PROTECTED_ROUTES.find(r => route.startsWith(r.path));
  if (!routeConfig) return true; // Public route
  
  // Check role hierarchy
  const roleHierarchy = [UserRole.USER, UserRole.SUBSCRIBER, UserRole.ADMIN];
  const userRoleIndex = roleHierarchy.indexOf(userRole);
  const requiredRoleIndex = roleHierarchy.indexOf(routeConfig.requiredRole);
  
  if (userRoleIndex < requiredRoleIndex) return false;
  
  // Check specific permissions if required
  if (routeConfig.requiredPermissions) {
    return hasAllPermissions(userRole, routeConfig.requiredPermissions);
  }
  
  return true;
};

export const getRoleDisplayName = (role: UserRole): string => {
  switch (role) {
    case UserRole.USER:
      return "Free User";
    case UserRole.SUBSCRIBER:
      return "Premium Subscriber";
    case UserRole.ADMIN:
      return "Administrator";
    default:
      return "Unknown";
  }
};

export const getRoleBadgeColor = (role: UserRole): string => {
  switch (role) {
    case UserRole.USER:
      return "bg-gray-100 text-gray-800";
    case UserRole.SUBSCRIBER:
      return "bg-blue-100 text-blue-800";
    case UserRole.ADMIN:
      return "bg-purple-100 text-purple-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

// JWT token payload interface
export interface JWTPayload {
  userId: string;
  email: string;
  role: UserRole;
  permissions: Permission[];
  iat: number;
  exp: number;
}
