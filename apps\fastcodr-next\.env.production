# FastCodr Backend Production Environment Configuration

# AI Model API Configuration (Required)
THIRD_API_URL=https://api.openai.com/v1
THIRD_API_KEY=your_production_openai_api_key

# JWT Secret (Production)
JWT_SECRET=your_production_jwt_secret_here

# MongoDB Connection (Production)
MONGODB_URI=***********************************************************************

# ScreenshotOne API Key for landing page screenshots
SCREENSHOTONE_API_KEY=your_screenshotone_api_key

# CORS Configuration
ALLOWED_ORIGINS=https://fastcodr.com,https://www.fastcodr.com

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_REQUESTS_PER_HOUR=1000

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/fastcodr/app.log

# Security
SECURE_COOKIES=true
HTTPS_ONLY=true

# Performance
NODE_ENV=production
ENABLE_COMPRESSION=true
CACHE_TTL=3600

# Monitoring
SENTRY_DSN=your_sentry_dsn
ENABLE_METRICS=true
