import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, MessageCircle } from 'lucide-react';
import AIAssistantPanel from './AIAssistantPanel';
import PermissionGuard from '@/components/auth/PermissionGuard';
import { Permission } from '@/types/auth';
import useUserStore from '@/stores/userSlice';

interface AIAssistantButtonProps {
  className?: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
}

const AIAssistantButton: React.FC<AIAssistantButtonProps> = ({
  className = '',
  position = 'bottom-right'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const { getRemainingUsage } = useUserStore();
  const remainingUsage = getRemainingUsage();

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'top-left':
        return 'top-4 left-4';
      case 'bottom-right':
      default:
        return 'bottom-4 right-4';
    }
  };

  const handleToggle = () => {
    if (isMinimized) {
      setIsMinimized(false);
    } else {
      setIsOpen(!isOpen);
    }
  };

  const handleMinimize = () => {
    setIsMinimized(true);
    setIsOpen(true);
  };

  const handleClose = () => {
    setIsOpen(false);
    setIsMinimized(false);
  };

  return (
    <>
      {/* Floating Button */}
      <PermissionGuard 
        requiredPermissions={[Permission.AI_ASSISTANT]}
        fallback={
          <div className={`fixed ${getPositionClasses()} z-40`}>
            <div className="relative group">
              <button
                onClick={() => window.location.href = '/subscribe'}
                className="w-14 h-14 bg-gradient-to-r from-gray-400 to-gray-500 hover:from-gray-500 hover:to-gray-600 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 transform hover:scale-110"
              >
                <Bot className="w-6 h-6" />
              </button>
              
              {/* Tooltip */}
              <div className="absolute bottom-full right-0 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                Upgrade to Premium for AI Assistant
                <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
              </div>
            </div>
          </div>
        }
      >
        <div className={`fixed ${getPositionClasses()} z-40 ${className}`}>
          <div className="relative group">
            {!isOpen && (
              <button
                onClick={handleToggle}
                className="w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 transform hover:scale-110 relative"
              >
                <Bot className="w-6 h-6" />
                
                {/* Premium badge */}
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-yellow-400 rounded-full flex items-center justify-center">
                  <Sparkles className="w-3 h-3 text-yellow-800" />
                </div>
                
                {/* Usage indicator */}
                {remainingUsage.aiCalls !== -1 && remainingUsage.aiCalls <= 10 && (
                  <div className="absolute -bottom-1 -left-1 w-6 h-4 bg-red-500 rounded-full flex items-center justify-center text-xs font-bold">
                    {remainingUsage.aiCalls}
                  </div>
                )}
              </button>
            )}
            
            {/* Tooltip */}
            {!isOpen && (
              <div className="absolute bottom-full right-0 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                AI Assistant
                {remainingUsage.aiCalls !== -1 && (
                  <span className="ml-2 text-yellow-300">({remainingUsage.aiCalls} calls left)</span>
                )}
                <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
              </div>
            )}
          </div>
        </div>
      </PermissionGuard>

      {/* AI Assistant Panel */}
      <AIAssistantPanel
        isOpen={isOpen}
        onClose={handleClose}
        onMinimize={handleMinimize}
        isMinimized={isMinimized}
      />
    </>
  );
};

export default AIAssistantButton;
