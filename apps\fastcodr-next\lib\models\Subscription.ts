// Subscription data models for FastCodr

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: 'USD' | 'INR';
  interval: 'monthly' | 'yearly';
  intervalCount: number;
  features: string[];
  limits: {
    projects: number; // -1 for unlimited
    aiCalls: number; // -1 for unlimited
    storage: number; // in MB, -1 for unlimited
    collaborators: number;
    templates: number;
    exportFormats: string[];
  };
  isPopular: boolean;
  isActive: boolean;
  trialDays?: number;
  stripePriceId?: string;
  cashfreePlanId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Subscription {
  id: string;
  userId: string;
  planId: string;
  status: 'active' | 'cancelled' | 'expired' | 'past_due' | 'trialing' | 'incomplete';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  canceledAt?: Date;
  trialStart?: Date;
  trialEnd?: Date;
  
  // Payment provider details
  provider: 'cashfree' | 'stripe' | 'razorpay';
  providerSubscriptionId: string;
  providerCustomerId: string;
  
  // Billing details
  amount: number;
  currency: string;
  taxAmount?: number;
  discountAmount?: number;
  
  // Usage tracking
  usage: {
    projects: number;
    aiCalls: number;
    storage: number;
    resetDate: Date;
  };
  
  // Metadata
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface PaymentMethod {
  id: string;
  userId: string;
  provider: 'cashfree' | 'stripe' | 'razorpay';
  providerPaymentMethodId: string;
  type: 'card' | 'upi' | 'netbanking' | 'wallet';
  
  // Card details (if applicable)
  card?: {
    brand: string;
    last4: string;
    expMonth: number;
    expYear: number;
    country?: string;
  };
  
  // UPI details (if applicable)
  upi?: {
    vpa: string;
  };
  
  isDefault: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Invoice {
  id: string;
  subscriptionId: string;
  userId: string;
  invoiceNumber: string;
  status: 'draft' | 'open' | 'paid' | 'void' | 'uncollectible';
  
  // Amount details
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  total: number;
  currency: string;
  
  // Dates
  invoiceDate: Date;
  dueDate: Date;
  paidAt?: Date;
  
  // Line items
  lineItems: {
    description: string;
    quantity: number;
    unitPrice: number;
    amount: number;
  }[];
  
  // Payment details
  paymentMethod?: string;
  transactionId?: string;
  
  // Provider details
  provider: string;
  providerInvoiceId: string;
  
  createdAt: Date;
  updatedAt: Date;
}

export interface CashfreeConfig {
  appId: string;
  secretKey: string;
  environment: 'sandbox' | 'production';
  webhookSecret: string;
}

export interface CashfreeSubscriptionRequest {
  subscriptionId: string;
  planId: string;
  customerId: string;
  customerEmail: string;
  customerPhone: string;
  returnUrl: string;
  notifyUrl: string;
  metadata?: Record<string, any>;
}

export interface CashfreeWebhookEvent {
  eventType: string;
  eventTime: string;
  data: {
    subscription: {
      subscriptionId: string;
      planId: string;
      status: string;
      currentCycle: number;
      nextBillingDate: string;
      customerId: string;
    };
    payment?: {
      paymentId: string;
      amount: number;
      currency: string;
      status: string;
      paymentMethod: string;
    };
  };
}

// Predefined subscription plans
export const SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
  {
    id: 'free',
    name: 'Free',
    description: 'Perfect for getting started with FastCodr',
    price: 0,
    currency: 'USD',
    interval: 'monthly',
    intervalCount: 1,
    features: [
      'Basic code editor',
      'Project saving',
      'Community templates',
      'Basic export options'
    ],
    limits: {
      projects: 3,
      aiCalls: 0,
      storage: 100, // 100MB
      collaborators: 0,
      templates: 5,
      exportFormats: ['zip']
    },
    isPopular: false,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'premium_monthly',
    name: 'Premium',
    description: 'Unlock the full power of FastCodr',
    price: 10,
    currency: 'USD',
    interval: 'monthly',
    intervalCount: 1,
    features: [
      'AI-powered coding assistant',
      'Visual drag-and-drop builder',
      'Unlimited projects',
      'Premium templates',
      'Advanced export options',
      'Priority support',
      'Real-time collaboration',
      'Custom themes'
    ],
    limits: {
      projects: -1, // unlimited
      aiCalls: 1000,
      storage: 1000, // 1GB
      collaborators: 5,
      templates: -1, // unlimited
      exportFormats: ['zip', 'git', 'docker', 'vercel']
    },
    isPopular: true,
    isActive: true,
    trialDays: 7,
    cashfreePlanId: 'fastcodr_premium_monthly',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'premium_yearly',
    name: 'Premium Yearly',
    description: 'Save 20% with annual billing',
    price: 96, // $8/month when billed yearly
    currency: 'USD',
    interval: 'yearly',
    intervalCount: 1,
    features: [
      'Everything in Premium',
      '20% discount',
      'Extended AI usage',
      'Advanced analytics',
      'Custom integrations'
    ],
    limits: {
      projects: -1,
      aiCalls: 15000, // More AI calls for yearly
      storage: 2000, // 2GB
      collaborators: 10,
      templates: -1,
      exportFormats: ['zip', 'git', 'docker', 'vercel', 'aws']
    },
    isPopular: false,
    isActive: true,
    trialDays: 14, // Longer trial for yearly
    cashfreePlanId: 'fastcodr_premium_yearly',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'For teams and organizations',
    price: 50,
    currency: 'USD',
    interval: 'monthly',
    intervalCount: 1,
    features: [
      'Everything in Premium',
      'Unlimited team members',
      'Advanced security',
      'SSO integration',
      'Custom deployment',
      'Dedicated support',
      'SLA guarantee',
      'Custom features'
    ],
    limits: {
      projects: -1,
      aiCalls: -1, // unlimited
      storage: -1, // unlimited
      collaborators: -1, // unlimited
      templates: -1,
      exportFormats: ['zip', 'git', 'docker', 'vercel', 'aws', 'kubernetes']
    },
    isPopular: false,
    isActive: true,
    cashfreePlanId: 'fastcodr_enterprise',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Utility functions
export const getPlanById = (planId: string): SubscriptionPlan | null => {
  return SUBSCRIPTION_PLANS.find(plan => plan.id === planId) || null;
};

export const getActivePlans = (): SubscriptionPlan[] => {
  return SUBSCRIPTION_PLANS.filter(plan => plan.isActive);
};

export const calculateProration = (
  currentPlan: SubscriptionPlan,
  newPlan: SubscriptionPlan,
  daysRemaining: number
): number => {
  const currentDailyRate = currentPlan.price / 30;
  const newDailyRate = newPlan.price / 30;
  const unusedCredit = currentDailyRate * daysRemaining;
  const newCharges = newDailyRate * daysRemaining;
  
  return Math.max(0, newCharges - unusedCredit);
};

export const isFeatureAvailable = (
  subscription: Subscription | null,
  feature: string
): boolean => {
  if (!subscription || subscription.status !== 'active') {
    const freePlan = getPlanById('free');
    return freePlan?.features.includes(feature) || false;
  }
  
  const plan = getPlanById(subscription.planId);
  return plan?.features.includes(feature) || false;
};

export const getRemainingUsage = (
  subscription: Subscription | null,
  plan: SubscriptionPlan | null
) => {
  if (!subscription || !plan) {
    const freePlan = getPlanById('free');
    return {
      projects: freePlan?.limits.projects || 0,
      aiCalls: freePlan?.limits.aiCalls || 0,
      storage: freePlan?.limits.storage || 0
    };
  }
  
  return {
    projects: plan.limits.projects === -1 ? -1 : Math.max(0, plan.limits.projects - subscription.usage.projects),
    aiCalls: plan.limits.aiCalls === -1 ? -1 : Math.max(0, plan.limits.aiCalls - subscription.usage.aiCalls),
    storage: plan.limits.storage === -1 ? -1 : Math.max(0, plan.limits.storage - subscription.usage.storage)
  };
};

export const formatPrice = (price: number, currency: string = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(price);
};

export const getTrialEndDate = (plan: SubscriptionPlan): Date | null => {
  if (!plan.trialDays) return null;
  
  const trialEnd = new Date();
  trialEnd.setDate(trialEnd.getDate() + plan.trialDays);
  return trialEnd;
};
