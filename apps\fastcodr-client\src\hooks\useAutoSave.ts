import { useEffect, useRef, useCallback } from 'react';
import useProjectStore from '@/stores/projectSlice';
import { useFileStore } from '@/components/CodeEditor/stores/fileStore';
import { toast } from 'react-hot-toast';

interface UseAutoSaveOptions {
  enabled?: boolean;
  interval?: number; // in milliseconds
  onSave?: () => void;
  onError?: (error: Error) => void;
}

export const useAutoSave = (options: UseAutoSaveOptions = {}) => {
  const {
    enabled = true,
    interval = 30000, // 30 seconds default
    onSave,
    onError
  } = options;

  const {
    currentProject,
    hasUnsavedChanges,
    autoSaveProject,
    isAutoSaving,
    autoSaveEnabled,
    updateCurrentProject
  } = useProjectStore();

  const { files } = useFileStore();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastSaveRef = useRef<Date | null>(null);

  // Convert file store files to project files format
  const convertFilesToProjectFormat = useCallback(() => {
    return Object.entries(files).map(([path, content]) => ({
      id: `file_${path.replace(/[^a-zA-Z0-9]/g, '_')}`,
      name: path.split('/').pop() || 'untitled',
      content,
      language: getLanguageFromPath(path),
      path,
      size: content.length,
      lastModified: new Date(),
      isDirectory: false
    }));
  }, [files]);

  // Get language from file path
  const getLanguageFromPath = (path: string): string => {
    const extension = path.split('.').pop()?.toLowerCase();
    const languageMap: Record<string, string> = {
      'ts': 'typescript',
      'tsx': 'typescript',
      'js': 'javascript',
      'jsx': 'javascript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'json': 'json',
      'md': 'markdown',
      'yaml': 'yaml',
      'yml': 'yaml'
    };
    return languageMap[extension || ''] || 'plaintext';
  };

  // Perform auto-save
  const performAutoSave = useCallback(async () => {
    if (!currentProject || !hasUnsavedChanges || !autoSaveEnabled || isAutoSaving) {
      return;
    }

    try {
      // Update project with current files
      const projectFiles = convertFilesToProjectFormat();
      updateCurrentProject({ files: projectFiles });

      // Perform auto-save
      await autoSaveProject();
      
      lastSaveRef.current = new Date();
      onSave?.();
      
      // Show subtle notification
      const toastId = toast.success('Auto-saved', {
        duration: 2000,
        position: 'bottom-right',
        style: {
          fontSize: '12px',
          padding: '8px 12px'
        }
      });
      
      // Auto-dismiss the toast
      setTimeout(() => toast.dismiss(toastId), 1500);
      
    } catch (error) {
      console.error('Auto-save failed:', error);
      onError?.(error as Error);
      
      // Show error notification
      toast.error('Auto-save failed', {
        duration: 3000,
        position: 'bottom-right'
      });
    }
  }, [
    currentProject,
    hasUnsavedChanges,
    autoSaveEnabled,
    isAutoSaving,
    convertFilesToProjectFormat,
    updateCurrentProject,
    autoSaveProject,
    onSave,
    onError
  ]);

  // Set up auto-save interval
  useEffect(() => {
    if (!enabled || !autoSaveEnabled || !currentProject) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Clear existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Set up new interval
    intervalRef.current = setInterval(performAutoSave, interval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [enabled, autoSaveEnabled, currentProject, interval, performAutoSave]);

  // Auto-save on page unload
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges && currentProject) {
        // Attempt synchronous save (limited time)
        performAutoSave();
        
        // Show browser warning
        e.preventDefault();
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
        return e.returnValue;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges, currentProject, performAutoSave]);

  // Manual save function
  const manualSave = useCallback(async () => {
    if (!currentProject) return;

    try {
      const projectFiles = convertFilesToProjectFormat();
      updateCurrentProject({ files: projectFiles });
      
      // Use the regular save function for manual saves
      await useProjectStore.getState().saveProject();
      
      lastSaveRef.current = new Date();
      onSave?.();
      
    } catch (error) {
      console.error('Manual save failed:', error);
      onError?.(error as Error);
      throw error;
    }
  }, [currentProject, convertFilesToProjectFormat, updateCurrentProject, onSave, onError]);

  // Force auto-save (useful for critical moments)
  const forceAutoSave = useCallback(() => {
    performAutoSave();
  }, [performAutoSave]);

  // Get auto-save status
  const getAutoSaveStatus = useCallback(() => {
    return {
      isEnabled: autoSaveEnabled && enabled,
      isActive: !!intervalRef.current,
      hasUnsavedChanges,
      isAutoSaving,
      lastSave: lastSaveRef.current,
      nextSave: lastSaveRef.current 
        ? new Date(lastSaveRef.current.getTime() + interval)
        : null,
      interval
    };
  }, [autoSaveEnabled, enabled, hasUnsavedChanges, isAutoSaving, interval]);

  return {
    // Status
    isAutoSaving,
    hasUnsavedChanges,
    lastSave: lastSaveRef.current,
    
    // Actions
    manualSave,
    forceAutoSave,
    performAutoSave,
    
    // Utils
    getAutoSaveStatus
  };
};

// Hook for auto-save settings
export const useAutoSaveSettings = () => {
  const {
    autoSaveEnabled,
    autoSaveInterval,
    updateAutoSaveSettings
  } = useProjectStore();

  const setEnabled = useCallback((enabled: boolean) => {
    updateAutoSaveSettings(enabled);
  }, [updateAutoSaveSettings]);

  const setInterval = useCallback((interval: number) => {
    updateAutoSaveSettings(autoSaveEnabled, interval);
  }, [autoSaveEnabled, updateAutoSaveSettings]);

  const setSettings = useCallback((enabled: boolean, interval: number) => {
    updateAutoSaveSettings(enabled, interval);
  }, [updateAutoSaveSettings]);

  return {
    enabled: autoSaveEnabled,
    interval: autoSaveInterval,
    setEnabled,
    setInterval,
    setSettings
  };
};

// Auto-save status indicator component hook
export const useAutoSaveIndicator = () => {
  const { isAutoSaving, hasUnsavedChanges, lastSaved } = useProjectStore();
  
  const getIndicatorText = useCallback(() => {
    if (isAutoSaving) {
      return 'Saving...';
    }
    
    if (hasUnsavedChanges) {
      return 'Unsaved changes';
    }
    
    if (lastSaved) {
      const now = new Date();
      const diff = now.getTime() - lastSaved.getTime();
      const minutes = Math.floor(diff / 60000);
      
      if (minutes < 1) {
        return 'Saved just now';
      } else if (minutes === 1) {
        return 'Saved 1 minute ago';
      } else if (minutes < 60) {
        return `Saved ${minutes} minutes ago`;
      } else {
        return 'Saved over an hour ago';
      }
    }
    
    return 'Not saved';
  }, [isAutoSaving, hasUnsavedChanges, lastSaved]);

  const getIndicatorColor = useCallback(() => {
    if (isAutoSaving) {
      return 'text-blue-600';
    }
    
    if (hasUnsavedChanges) {
      return 'text-orange-600';
    }
    
    return 'text-green-600';
  }, [isAutoSaving, hasUnsavedChanges]);

  return {
    text: getIndicatorText(),
    color: getIndicatorColor(),
    isAutoSaving,
    hasUnsavedChanges
  };
};
