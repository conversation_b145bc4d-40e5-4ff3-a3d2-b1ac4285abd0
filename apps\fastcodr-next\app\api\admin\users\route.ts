import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, UserRole, Permission } from '@/lib/auth';

interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  status: 'active' | 'suspended' | 'pending';
  subscription: {
    plan: 'free' | 'premium' | 'enterprise';
    status: 'active' | 'cancelled' | 'expired';
    startDate: Date;
    endDate?: Date;
    amount: number;
  } | null;
  usage: {
    projects: number;
    aiCalls: number;
    storage: number;
    lastActive: Date;
  };
  createdAt: Date;
  lastLoginAt?: Date;
  loginCount: number;
  ipAddress?: string;
}

// GET /api/admin/users - Get all users with filtering (Admin only)
export async function GET(request: NextRequest) {
  const authResult = await requireAuth(UserRole.ADMIN, [Permission.MANAGE_USERS])(request);

  if (authResult instanceof Response) {
    return authResult;
  }

  try {
    const { searchParams } = new URL(request.url);
    const role = searchParams.get('role') as UserRole;
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Enhanced mock user data
    const mockUsers: AdminUser[] = [
      {
        id: 'user_1',
        email: '<EMAIL>',
        name: 'John Doe',
        role: UserRole.SUBSCRIBER,
        status: 'active',
        subscription: {
          plan: 'premium',
          status: 'active',
          startDate: new Date('2024-01-15'),
          amount: 10
        },
        usage: {
          projects: 12,
          aiCalls: 456,
          storage: 1024 * 1024 * 50,
          lastActive: new Date('2024-01-22')
        },
        createdAt: new Date('2024-01-10'),
        lastLoginAt: new Date('2024-01-22'),
        loginCount: 45,
        ipAddress: '*************'
      },
      {
        id: 'user_2',
        email: '<EMAIL>',
        name: 'Jane Smith',
        role: UserRole.USER,
        status: 'active',
        subscription: null,
        usage: {
          projects: 3,
          aiCalls: 0,
          storage: 1024 * 1024 * 5,
          lastActive: new Date('2024-01-21')
        },
        createdAt: new Date('2024-01-18'),
        lastLoginAt: new Date('2024-01-21'),
        loginCount: 12,
        ipAddress: '*************'
      },
      {
        id: 'user_3',
        email: '<EMAIL>',
        name: 'FastCodr Admin',
        role: UserRole.ADMIN,
        status: 'active',
        subscription: null,
        usage: {
          projects: 25,
          aiCalls: 1200,
          storage: 1024 * 1024 * 200,
          lastActive: new Date()
        },
        createdAt: new Date('2023-12-01'),
        lastLoginAt: new Date(),
        loginCount: 234,
        ipAddress: '********'
      },
      {
        id: 'user_4',
        email: '<EMAIL>',
        name: 'Suspended User',
        role: UserRole.USER,
        status: 'suspended',
        subscription: null,
        usage: {
          projects: 1,
          aiCalls: 0,
          storage: 1024 * 1024 * 2,
          lastActive: new Date('2024-01-10')
        },
        createdAt: new Date('2024-01-05'),
        lastLoginAt: new Date('2024-01-10'),
        loginCount: 5,
        ipAddress: '*************'
      }
    ];

    // Apply filters
    let filteredUsers = mockUsers.filter(user => {
      if (role && user.role !== role) return false;
      if (status && user.status !== status) return false;
      if (search) {
        const searchLower = search.toLowerCase();
        if (!user.name.toLowerCase().includes(searchLower) &&
            !user.email.toLowerCase().includes(searchLower)) return false;
      }
      return true;
    });

    // Apply pagination
    const total = filteredUsers.length;
    const users = filteredUsers.slice(offset, offset + limit);

    // Calculate stats
    const stats = {
      total: mockUsers.length,
      active: mockUsers.filter(u => u.status === 'active').length,
      suspended: mockUsers.filter(u => u.status === 'suspended').length,
      subscribers: mockUsers.filter(u => u.subscription?.status === 'active').length,
      admins: mockUsers.filter(u => u.role === UserRole.ADMIN).length
    };

    return NextResponse.json({
      success: true,
      data: {
        users,
        stats,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      }
    });

  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/users - Update user role (Admin only)
export async function PUT(request: NextRequest) {
  const authResult = await requireAuth(UserRole.ADMIN, [Permission.MANAGE_USERS])(request);

  if (authResult instanceof Response) {
    return authResult;
  }

  try {
    const { userId, role, isActive } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    if (role && !Object.values(UserRole).includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role' },
        { status: 400 }
      );
    }

    // Mock update - replace with actual database update
    const updatedUser = {
      id: userId,
      role: role || UserRole.USER,
      isActive: isActive !== undefined ? isActive : true,
      updatedAt: new Date()
    };

    return NextResponse.json({
      success: true,
      data: updatedUser,
      message: 'User updated successfully'
    });

  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/users - Delete user (Admin only)
export async function DELETE(request: NextRequest) {
  const authResult = await requireAuth(UserRole.ADMIN, [Permission.MANAGE_USERS])(request);

  if (authResult instanceof Response) {
    return authResult;
  }

  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Prevent admin from deleting themselves
    if (userId === authResult.user.userId) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      );
    }

    // Mock deletion - replace with actual database deletion
    // In a real app, you might want to soft delete instead

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    );
  }
}
