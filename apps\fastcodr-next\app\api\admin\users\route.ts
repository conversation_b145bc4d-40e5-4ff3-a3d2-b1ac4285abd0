import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, UserRole, Permission } from '@/lib/auth';

// GET /api/admin/users - Get all users (Admin only)
export async function GET(request: NextRequest) {
  // Protect route with admin role and specific permissions
  const authResult = await requireAuth(UserRole.ADMIN, [Permission.MANAGE_USERS])(request);
  
  if (authResult instanceof Response) {
    return authResult; // Return error response
  }

  const { user } = authResult;

  try {
    // Mock user data - replace with actual database query
    const users = [
      {
        id: '1',
        email: '<EMAIL>',
        username: 'user1',
        role: UserRole.USER,
        isActive: true,
        createdAt: new Date(),
        currentUsage: {
          projectsThisMonth: 2,
          aiCallsThisMonth: 25,
          storageUsed: 50
        }
      },
      {
        id: '2',
        email: '<EMAIL>',
        username: 'subscriber1',
        role: UserRole.SUBSCRIBER,
        isActive: true,
        createdAt: new Date(),
        currentUsage: {
          projectsThisMonth: 15,
          aiCallsThisMonth: 300,
          storageUsed: 500
        }
      }
    ];

    return NextResponse.json({
      success: true,
      data: users,
      total: users.length
    });

  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/users - Update user role (Admin only)
export async function PUT(request: NextRequest) {
  const authResult = await requireAuth(UserRole.ADMIN, [Permission.MANAGE_USERS])(request);
  
  if (authResult instanceof Response) {
    return authResult;
  }

  try {
    const { userId, role, isActive } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    if (role && !Object.values(UserRole).includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role' },
        { status: 400 }
      );
    }

    // Mock update - replace with actual database update
    const updatedUser = {
      id: userId,
      role: role || UserRole.USER,
      isActive: isActive !== undefined ? isActive : true,
      updatedAt: new Date()
    };

    return NextResponse.json({
      success: true,
      data: updatedUser,
      message: 'User updated successfully'
    });

  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/users - Delete user (Admin only)
export async function DELETE(request: NextRequest) {
  const authResult = await requireAuth(UserRole.ADMIN, [Permission.MANAGE_USERS])(request);
  
  if (authResult instanceof Response) {
    return authResult;
  }

  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Prevent admin from deleting themselves
    if (userId === authResult.user.userId) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      );
    }

    // Mock deletion - replace with actual database deletion
    // In a real app, you might want to soft delete instead
    
    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    );
  }
}
