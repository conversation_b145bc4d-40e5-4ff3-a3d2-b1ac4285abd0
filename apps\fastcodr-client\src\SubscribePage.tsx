import React, { useState, useEffect } from 'react';
import { createSubscriptionLink } from './lib/cashfree';
import useSubscriptionStore from './stores/subscriptionSlice';
import useUserStore from './stores/userSlice';
import { Crown, Check, Zap, Code, Rocket, Shield, ArrowLeft } from 'lucide-react';

const PLAN = {
  name: 'FastCodr Premium',
  price: '$10',
  period: 'month',
  features: [
    'Unlimited AI code generation',
    'Advanced debugging tools',
    'Premium templates & snippets',
    'Priority customer support',
    'Early access to new features',
    'Export projects anywhere',
    'Team collaboration tools',
    'Advanced analytics dashboard',
  ],
};

export default function SubscribePage() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { isSubscribed } = useSubscriptionStore();
  const { user } = useUserStore();

  // Redirect if already subscribed
  useEffect(() => {
    if (isSubscribed) {
      window.location.href = '/';
    }
  }, [isSubscribed]);

  const handleSubscribe = async () => {
    setLoading(true);
    setError('');
    try {
      // Use actual user info if available, otherwise prompt for email
      const userInfo = user ?
        { email: user.email, name: user.username } :
        { email: '<EMAIL>', name: 'FastCodr User' };

      const res = await createSubscriptionLink(userInfo);
      if (res && res.subscription_link) {
        // Store subscription ID for later verification
        if (res.subscription_id) {
          localStorage.setItem('pending_subscription_id', res.subscription_id);
        }
        window.location.href = res.subscription_link;
      } else {
        setError(res.error || 'Failed to get payment link.');
      }
    } catch (e: any) {
      setError(e.message || 'Payment initiation failed.');
    } finally {
      setLoading(false);
    }
  };

  const handleGoBack = () => {
    window.history.back();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900">
      {/* Header */}
      <div className="flex items-center justify-between p-6">
        <button
          onClick={handleGoBack}
          className="flex items-center gap-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          Back
        </button>

        <div className="flex items-center gap-2">
          <Crown className="w-6 h-6 text-yellow-500" />
          <span className="text-lg font-semibold text-gray-800 dark:text-gray-200">FastCodr Premium</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-col items-center justify-center px-6 py-12">
        <div className="max-w-4xl w-full">
          {/* Hero Section */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full">
                <Zap className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {PLAN.name}
              </h1>
            </div>
            <p className="text-xl text-gray-600 dark:text-gray-400 mb-6">
              Supercharge your coding workflow with AI-powered tools
            </p>
            <div className="flex items-center justify-center gap-2 text-3xl font-bold text-gray-800 dark:text-gray-200">
              <span>{PLAN.price}</span>
              <span className="text-lg text-gray-500">/ {PLAN.period}</span>
            </div>
          </div>

          {/* Features Grid */}
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <h3 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Premium Features</h3>
              <ul className="space-y-3">
                {PLAN.features.map((feature, index) => (
                  <li key={index} className="flex items-center gap-3">
                    <div className="flex-shrink-0 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <Check className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <h3 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Why Choose Premium?</h3>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <Code className="w-5 h-5 text-blue-500 mt-1" />
                  <div>
                    <h4 className="font-medium text-gray-800 dark:text-gray-200">Advanced AI</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Access to the latest AI models for better code generation</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <Rocket className="w-5 h-5 text-purple-500 mt-1" />
                  <div>
                    <h4 className="font-medium text-gray-800 dark:text-gray-200">Faster Development</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Boost productivity with premium tools and templates</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <Shield className="w-5 h-5 text-green-500 mt-1" />
                  <div>
                    <h4 className="font-medium text-gray-800 dark:text-gray-200">Priority Support</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Get help when you need it with dedicated support</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div className="text-center">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg max-w-md mx-auto">
              <button
                onClick={handleSubscribe}
                disabled={loading}
                className="w-full py-4 px-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold text-lg rounded-lg shadow-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                {loading ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Redirecting to Payment...
                  </div>
                ) : (
                  <div className="flex items-center justify-center gap-2">
                    <Crown className="w-5 h-5" />
                    Subscribe Now - {PLAN.price}/{PLAN.period}
                  </div>
                )}
              </button>

              {error && (
                <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                  <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
                </div>
              )}

              <p className="text-xs text-gray-500 dark:text-gray-400 mt-4">
                Secure payment powered by Cashfree. Cancel anytime.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
