// Project data models for FastCodr

export interface ProjectFile {
  id: string;
  name: string;
  content: string;
  language: string;
  path: string;
  size: number;
  lastModified: Date;
  isDirectory: boolean;
  children?: ProjectFile[];
}

export interface ProjectSettings {
  theme: 'light' | 'dark';
  fontSize: number;
  tabSize: number;
  wordWrap: boolean;
  minimap: boolean;
  autoSave: boolean;
  preferredEditor: 'codemirror' | 'monaco' | 'visual';
}

export interface ProjectMetadata {
  framework: string;
  language: string;
  template: string;
  dependencies: string[];
  buildCommand?: string;
  startCommand?: string;
  deployUrl?: string;
  repository?: string;
}

export interface Project {
  id: string;
  name: string;
  description: string;
  userId: string;
  files: ProjectFile[];
  settings: ProjectSettings;
  metadata: ProjectMetadata;
  tags: string[];
  isPublic: boolean;
  isTemplate: boolean;
  thumbnail?: string;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  lastOpenedAt?: Date;
  
  // Collaboration
  collaborators: string[];
  shareToken?: string;
  
  // Analytics
  viewCount: number;
  forkCount: number;
  starCount: number;
  
  // Status
  status: 'draft' | 'published' | 'archived';
  version: string;
  
  // Auto-save
  isDraft: boolean;
  draftSavedAt?: Date;
  
  // Size and limits
  totalSize: number;
  fileCount: number;
}

export interface ProjectTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  framework: string;
  language: string;
  thumbnail: string;
  files: ProjectFile[];
  isPremium: boolean;
  tags: string[];
  createdBy: string;
  usageCount: number;
  rating: number;
  createdAt: Date;
}

export interface ProjectShare {
  id: string;
  projectId: string;
  shareToken: string;
  permissions: 'view' | 'edit' | 'admin';
  expiresAt?: Date;
  createdBy: string;
  createdAt: Date;
  accessCount: number;
  lastAccessedAt?: Date;
}

// Project creation request
export interface CreateProjectRequest {
  name: string;
  description?: string;
  template?: string;
  framework?: string;
  language?: string;
  isPublic?: boolean;
  files?: Partial<ProjectFile>[];
}

// Project update request
export interface UpdateProjectRequest {
  name?: string;
  description?: string;
  files?: ProjectFile[];
  settings?: Partial<ProjectSettings>;
  metadata?: Partial<ProjectMetadata>;
  tags?: string[];
  isPublic?: boolean;
  status?: Project['status'];
}

// Project query filters
export interface ProjectFilters {
  userId?: string;
  framework?: string;
  language?: string;
  tags?: string[];
  isPublic?: boolean;
  isTemplate?: boolean;
  status?: Project['status'];
  search?: string;
  sortBy?: 'createdAt' | 'updatedAt' | 'name' | 'viewCount' | 'starCount';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

// Project statistics
export interface ProjectStats {
  totalProjects: number;
  publicProjects: number;
  privateProjects: number;
  draftProjects: number;
  totalSize: number;
  averageSize: number;
  mostUsedFrameworks: { framework: string; count: number }[];
  mostUsedLanguages: { language: string; count: number }[];
  recentActivity: {
    date: string;
    projectsCreated: number;
    projectsUpdated: number;
  }[];
}

// Default project settings
export const DEFAULT_PROJECT_SETTINGS: ProjectSettings = {
  theme: 'dark',
  fontSize: 14,
  tabSize: 2,
  wordWrap: true,
  minimap: true,
  autoSave: true,
  preferredEditor: 'monaco'
};

// Default project metadata
export const DEFAULT_PROJECT_METADATA: ProjectMetadata = {
  framework: 'react',
  language: 'typescript',
  template: 'blank',
  dependencies: [],
  buildCommand: 'npm run build',
  startCommand: 'npm start'
};

// Project templates
export const PROJECT_TEMPLATES: ProjectTemplate[] = [
  {
    id: 'react-typescript',
    name: 'React + TypeScript',
    description: 'Modern React application with TypeScript',
    category: 'Frontend',
    framework: 'react',
    language: 'typescript',
    thumbnail: '/templates/react-ts.png',
    isPremium: false,
    tags: ['react', 'typescript', 'frontend'],
    createdBy: 'system',
    usageCount: 0,
    rating: 5,
    createdAt: new Date(),
    files: [
      {
        id: 'app-tsx',
        name: 'App.tsx',
        content: `import React from 'react';
import './App.css';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to FastCodr</h1>
        <p>Start building your amazing project!</p>
      </header>
    </div>
  );
}

export default App;`,
        language: 'typescript',
        path: '/App.tsx',
        size: 0,
        lastModified: new Date(),
        isDirectory: false
      },
      {
        id: 'index-tsx',
        name: 'index.tsx',
        content: `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`,
        language: 'typescript',
        path: '/index.tsx',
        size: 0,
        lastModified: new Date(),
        isDirectory: false
      }
    ]
  },
  {
    id: 'nextjs-app',
    name: 'Next.js App',
    description: 'Full-stack Next.js application',
    category: 'Full-stack',
    framework: 'nextjs',
    language: 'typescript',
    thumbnail: '/templates/nextjs.png',
    isPremium: true,
    tags: ['nextjs', 'react', 'typescript', 'fullstack'],
    createdBy: 'system',
    usageCount: 0,
    rating: 5,
    createdAt: new Date(),
    files: []
  },
  {
    id: 'vue-composition',
    name: 'Vue 3 + Composition API',
    description: 'Modern Vue.js application with Composition API',
    category: 'Frontend',
    framework: 'vue',
    language: 'typescript',
    thumbnail: '/templates/vue.png',
    isPremium: true,
    tags: ['vue', 'typescript', 'composition-api'],
    createdBy: 'system',
    usageCount: 0,
    rating: 5,
    createdAt: new Date(),
    files: []
  }
];

// Utility functions
export const calculateProjectSize = (files: ProjectFile[]): number => {
  return files.reduce((total, file) => {
    if (file.isDirectory && file.children) {
      return total + calculateProjectSize(file.children);
    }
    return total + file.size;
  }, 0);
};

export const countProjectFiles = (files: ProjectFile[]): number => {
  return files.reduce((total, file) => {
    if (file.isDirectory && file.children) {
      return total + countProjectFiles(file.children);
    }
    return total + 1;
  }, 0);
};

export const findFileInProject = (files: ProjectFile[], path: string): ProjectFile | null => {
  for (const file of files) {
    if (file.path === path) {
      return file;
    }
    if (file.isDirectory && file.children) {
      const found = findFileInProject(file.children, path);
      if (found) return found;
    }
  }
  return null;
};
