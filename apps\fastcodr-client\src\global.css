@tailwind base;
@tailwind components;
@tailwind utilities;
/* 全局光标样式 */
input, textarea, [contenteditable="true"] {
  caret-color: #000000 !important;
}
.CodeMirror-cursor {
   border-left: 2px solid #ff0000 !important; /* 设置光标颜色为红色，宽度为2px */
}
/* 暗黑模式下的光标样式 */
@media (prefers-color-scheme: dark) {
  input, textarea, [contenteditable="true"] {
    caret-color: #ffffff;
  }
}

/* 如果使用 Tailwind 的 dark 类控制暗黑模式 */
.dark input,
.dark textarea,
.dark [contenteditable="true"] {
  caret-color: #ffffff;
} 
@layer base {
  :root {
    --background: 238 100% 97%; /* fastcodr light blue */
    --border: 245 60% 80%; /* fastcodr border blue */
    --foreground: 245 60% 20%; /* fastcodr dark text */
  }

  .dark {
    --background: 252 60% 12%; /* fastcodr dark blue/purple */
    --border: 252 40% 30%; /* fastcodr dark border */
    --foreground: 238 100% 97%; /* fastcodr light text */
  }
} 