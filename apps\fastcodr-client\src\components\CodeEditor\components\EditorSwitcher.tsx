import React, { useState } from 'react';
import { Code, Paintbrush, Zap, Setting<PERSON>, Monitor } from 'lucide-react';
import { Editor as CodeMirrorEditor } from './Editor';
import MonacoEditor from './MonacoEditor/MonacoEditor';
import VisualBuilder from '../../VisualBuilder/VisualBuilder';
import PermissionGuard from '@/components/auth/PermissionGuard';
import { Permission } from '@/types/auth';
import { useFileStore } from '../stores/fileStore';
import { toast } from 'react-hot-toast';

type EditorType = 'codemirror' | 'monaco' | 'visual';

interface EditorSwitcherProps {
  fileName: string;
  initialLine?: number;
  className?: string;
}

const EditorSwitcher: React.FC<EditorSwitcherProps> = ({ 
  fileName, 
  initialLine, 
  className = '' 
}) => {
  const [activeEditor, setActiveEditor] = useState<EditorType>('codemirror');
  const [showSettings, setShowSettings] = useState(false);
  const { addFile } = useFileStore();

  // Determine if file is suitable for visual builder
  const isVisualBuilderCompatible = () => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    return ['tsx', 'jsx', 'html'].includes(extension || '');
  };

  const handleCodeGenerate = (code: string) => {
    const newFileName = fileName.replace(/\.(tsx|jsx|html)$/, '-generated.$1');
    addFile(newFileName, code);
    toast.success(`Generated code saved to ${newFileName}`);
  };

  const editorOptions = [
    {
      id: 'codemirror' as EditorType,
      name: 'CodeMirror',
      description: 'Lightweight, fast editor',
      icon: Code,
      features: ['Fast performance', 'Vim keybindings', 'Lightweight'],
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      id: 'monaco' as EditorType,
      name: 'Monaco Editor',
      description: 'VS Code-like experience',
      icon: Zap,
      features: ['IntelliSense', 'Rich autocomplete', 'Advanced features'],
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    },
    {
      id: 'visual' as EditorType,
      name: 'Visual Builder',
      description: 'Drag & drop UI builder',
      icon: Paintbrush,
      features: ['Visual design', 'Drag & drop', 'No code required'],
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      disabled: !isVisualBuilderCompatible()
    }
  ];

  const renderEditor = () => {
    switch (activeEditor) {
      case 'monaco':
        return (
          <MonacoEditor
            fileName={fileName}
            initialLine={initialLine}
            className="h-full"
          />
        );
      
      case 'visual':
        if (!isVisualBuilderCompatible()) {
          return (
            <div className="h-full flex items-center justify-center bg-gray-50">
              <div className="text-center">
                <Paintbrush className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Visual Builder Not Available
                </h3>
                <p className="text-gray-600 mb-4">
                  Visual builder is only available for React (.tsx, .jsx) and HTML files.
                </p>
                <button
                  onClick={() => setActiveEditor('codemirror')}
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  Switch to Code Editor
                </button>
              </div>
            </div>
          );
        }
        
        return (
          <PermissionGuard 
            requiredPermissions={[Permission.PREMIUM_TEMPLATES]}
            fallback={
              <div className="h-full flex items-center justify-center bg-gray-50">
                <div className="text-center max-w-md">
                  <Paintbrush className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Premium Feature
                  </h3>
                  <p className="text-gray-600 mb-4">
                    The Visual Builder is a premium feature. Upgrade to FastCodr Premium to access drag-and-drop UI building.
                  </p>
                  <button
                    onClick={() => window.location.href = '/subscribe'}
                    className="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg hover:from-blue-600 hover:to-purple-600 transition-all transform hover:scale-105"
                  >
                    Upgrade to Premium
                  </button>
                </div>
              </div>
            }
          >
            <VisualBuilder
              onCodeGenerate={handleCodeGenerate}
              className="h-full"
            />
          </PermissionGuard>
        );
      
      case 'codemirror':
      default:
        return (
          <CodeMirrorEditor
            fileName={fileName}
            initialLine={initialLine}
          />
        );
    }
  };

  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* Editor Toolbar */}
      <div className="flex items-center justify-between p-2 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-1">
          {editorOptions.map((option) => {
            const Icon = option.icon;
            const isActive = activeEditor === option.id;
            const isDisabled = option.disabled;
            
            return (
              <button
                key={option.id}
                onClick={() => !isDisabled && setActiveEditor(option.id)}
                disabled={isDisabled}
                className={`
                  flex items-center gap-2 px-3 py-1.5 rounded-lg text-sm font-medium transition-all
                  ${isActive 
                    ? `${option.color} ${option.bgColor} ${option.borderColor} border` 
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700'
                  }
                  ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                `}
                title={isDisabled ? 'Not available for this file type' : option.description}
              >
                <Icon className="w-4 h-4" />
                {option.name}
                {option.id === 'visual' && (
                  <span className="text-xs bg-yellow-100 text-yellow-800 px-1.5 py-0.5 rounded-full">
                    Premium
                  </span>
                )}
              </button>
            );
          })}
        </div>

        <div className="flex items-center gap-2">
          {/* Editor Info */}
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {fileName} • {editorOptions.find(opt => opt.id === activeEditor)?.name}
          </div>
          
          {/* Settings Button */}
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
            title="Editor Settings"
          >
            <Settings className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {editorOptions.map((option) => {
              const Icon = option.icon;
              const isActive = activeEditor === option.id;
              
              return (
                <div
                  key={option.id}
                  className={`
                    p-3 rounded-lg border cursor-pointer transition-all
                    ${isActive 
                      ? `${option.bgColor} ${option.borderColor} border-2` 
                      : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:border-gray-300'
                    }
                    ${option.disabled ? 'opacity-50 cursor-not-allowed' : ''}
                  `}
                  onClick={() => !option.disabled && setActiveEditor(option.id)}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Icon className={`w-5 h-5 ${isActive ? option.color : 'text-gray-500'}`} />
                    <h4 className={`font-medium ${isActive ? option.color : 'text-gray-900 dark:text-gray-100'}`}>
                      {option.name}
                    </h4>
                    {option.id === 'visual' && (
                      <span className="text-xs bg-yellow-100 text-yellow-800 px-1.5 py-0.5 rounded-full">
                        Premium
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    {option.description}
                  </p>
                  <ul className="text-xs text-gray-500 dark:text-gray-500 space-y-1">
                    {option.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-1">
                        <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Editor Content */}
      <div className="flex-1 overflow-hidden">
        {renderEditor()}
      </div>
    </div>
  );
};

export default EditorSwitcher;
