import React from 'react';
import { getSubscriptionStatus } from './lib/cashfree';
import useSubscriptionStore from './stores/subscriptionSlice';
import { Crown, Zap } from 'lucide-react';

interface GoPremiumCTAProps {
  variant?: 'button' | 'banner' | 'inline';
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
}

export function GoPremiumCTA({
  variant = 'button',
  size = 'md',
  showIcon = true
}: GoPremiumCTAProps) {
  const { isSubscribed } = useSubscriptionStore();
  const legacySubscribed = getSubscriptionStatus();

  // Don't show if user is subscribed
  if (isSubscribed || legacySubscribed) return null;

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  const baseClasses = `inline-flex items-center gap-2 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold shadow-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 ${sizeClasses[size]}`;

  if (variant === 'banner') {
    return (
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full">
              <Crown className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-800 dark:text-gray-200">
                Unlock Premium Features
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Get unlimited access to all FastCodr tools for just $10/month
              </p>
            </div>
          </div>
          <a href="/subscribe" className={baseClasses}>
            {showIcon && <Zap className="w-4 h-4" />}
            Upgrade Now
          </a>
        </div>
      </div>
    );
  }

  if (variant === 'inline') {
    return (
      <span className="inline-flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white text-xs font-semibold rounded-full">
        {showIcon && <Crown className="w-3 h-3" />}
        <a href="/subscribe" className="hover:underline">
          Go Premium
        </a>
      </span>
    );
  }

  // Default button variant
  return (
    <a href="/subscribe" className={baseClasses}>
      {showIcon && <Crown className="w-4 h-4" />}
      Go Premium
    </a>
  );
}
