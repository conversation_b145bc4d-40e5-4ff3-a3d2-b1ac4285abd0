import React, { useState, useEffect } from 'react';
import {
  Users,
  TrendingUp,
  DollarSign,
  Activity,
  Server,
  Database,
  Shield,
  Settings,
  BarChart3,
  Pie<PERSON><PERSON>,
  Calendar,
  Download,
  Refresh<PERSON>w,
  Al<PERSON><PERSON>riangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { Permission, UserRole } from '@/types/auth';
import useUserStore from '@/stores/userSlice';
import { toast } from 'react-hot-toast';
import { formatDistanceToNow } from 'date-fns';

interface DashboardStats {
  users: {
    total: number;
    active: number;
    new: number;
    subscribers: number;
    growth: number;
  };
  projects: {
    total: number;
    public: number;
    private: number;
    growth: number;
  };
  subscriptions: {
    active: number;
    revenue: number;
    mrr: number;
    growth: number;
  };
  system: {
    uptime: number;
    responseTime: number;
    errorRate: number;
    storageUsed: number;
    storageLimit: number;
  };
}

const AdminDashboard: React.FC = () => {
  const { token } = useUserStore();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d');
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchDashboardStats = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`${process.env.APP_BASE_URL}/api/admin/dashboard?timeRange=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch dashboard stats');
      }

      const data = await response.json();
      setStats(data.data.stats);
      setLastUpdated(new Date(data.data.lastUpdated));
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardStats();
  }, [timeRange]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return 'text-green-600';
    if (growth < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getSystemStatus = () => {
    if (!stats) return { status: 'unknown', color: 'gray' };
    
    if (stats.system.uptime > 99.5 && stats.system.errorRate < 1) {
      return { status: 'healthy', color: 'green' };
    } else if (stats.system.uptime > 98 && stats.system.errorRate < 5) {
      return { status: 'warning', color: 'yellow' };
    } else {
      return { status: 'critical', color: 'red' };
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  const systemStatus = getSystemStatus();

  return (
    <ProtectedRoute requiredRole={UserRole.ADMIN} requiredPermissions={[Permission.ADMIN_DASHBOARD]}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  Admin Dashboard
                </h1>
                <p className="mt-2 text-gray-600 dark:text-gray-400">
                  FastCodr system overview and management
                </p>
              </div>
              
              <div className="flex items-center gap-4">
                {/* Time Range Selector */}
                <select
                  value={timeRange}
                  onChange={(e) => setTimeRange(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="7d">Last 7 days</option>
                  <option value="30d">Last 30 days</option>
                  <option value="90d">Last 90 days</option>
                  <option value="1y">Last year</option>
                </select>
                
                {/* Refresh Button */}
                <button
                  onClick={fetchDashboardStats}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
                >
                  <RefreshCw className="w-4 h-4" />
                  Refresh
                </button>
              </div>
            </div>
            
            {/* Last Updated */}
            {lastUpdated && (
              <div className="mt-4 flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                <Clock className="w-4 h-4" />
                Last updated {formatDistanceToNow(lastUpdated, { addSuffix: true })}
              </div>
            )}
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* System Status Banner */}
          <div className={`mb-8 p-4 rounded-lg border ${
            systemStatus.color === 'green' ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800' :
            systemStatus.color === 'yellow' ? 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800' :
            'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
          }`}>
            <div className="flex items-center gap-3">
              {systemStatus.color === 'green' ? (
                <CheckCircle className="w-5 h-5 text-green-600" />
              ) : systemStatus.color === 'yellow' ? (
                <AlertTriangle className="w-5 h-5 text-yellow-600" />
              ) : (
                <AlertTriangle className="w-5 h-5 text-red-600" />
              )}
              
              <div>
                <h3 className={`font-medium ${
                  systemStatus.color === 'green' ? 'text-green-800 dark:text-green-200' :
                  systemStatus.color === 'yellow' ? 'text-yellow-800 dark:text-yellow-200' :
                  'text-red-800 dark:text-red-200'
                }`}>
                  System Status: {systemStatus.status.charAt(0).toUpperCase() + systemStatus.status.slice(1)}
                </h3>
                <p className={`text-sm ${
                  systemStatus.color === 'green' ? 'text-green-700 dark:text-green-300' :
                  systemStatus.color === 'yellow' ? 'text-yellow-700 dark:text-yellow-300' :
                  'text-red-700 dark:text-red-300'
                }`}>
                  {stats && `Uptime: ${stats.system.uptime}% • Response Time: ${stats.system.responseTime}ms • Error Rate: ${stats.system.errorRate}%`}
                </p>
              </div>
            </div>
          </div>

          {/* Stats Grid */}
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {/* Users Stats */}
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Users</p>
                    <p className="text-3xl font-bold text-gray-900 dark:text-white">{stats.users.total.toLocaleString()}</p>
                  </div>
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center gap-2">
                  <span className={`text-sm font-medium ${getGrowthColor(stats.users.growth)}`}>
                    {stats.users.growth > 0 ? '+' : ''}{stats.users.growth}%
                  </span>
                  <span className="text-sm text-gray-500">vs last period</span>
                </div>
                <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  {stats.users.active} active • {stats.users.subscribers} subscribers
                </div>
              </div>

              {/* Projects Stats */}
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Projects</p>
                    <p className="text-3xl font-bold text-gray-900 dark:text-white">{stats.projects.total.toLocaleString()}</p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                    <BarChart3 className="w-6 h-6 text-green-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center gap-2">
                  <span className={`text-sm font-medium ${getGrowthColor(stats.projects.growth)}`}>
                    {stats.projects.growth > 0 ? '+' : ''}{stats.projects.growth}%
                  </span>
                  <span className="text-sm text-gray-500">vs last period</span>
                </div>
                <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  {stats.projects.public} public • {stats.projects.private} private
                </div>
              </div>

              {/* Revenue Stats */}
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Monthly Revenue</p>
                    <p className="text-3xl font-bold text-gray-900 dark:text-white">{formatCurrency(stats.subscriptions.mrr)}</p>
                  </div>
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                    <DollarSign className="w-6 h-6 text-purple-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center gap-2">
                  <span className={`text-sm font-medium ${getGrowthColor(stats.subscriptions.growth)}`}>
                    {stats.subscriptions.growth > 0 ? '+' : ''}{stats.subscriptions.growth}%
                  </span>
                  <span className="text-sm text-gray-500">vs last period</span>
                </div>
                <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  {stats.subscriptions.active} active subscriptions
                </div>
              </div>

              {/* System Stats */}
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Storage Used</p>
                    <p className="text-3xl font-bold text-gray-900 dark:text-white">
                      {Math.round((stats.system.storageUsed / stats.system.storageLimit) * 100)}%
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                    <Database className="w-6 h-6 text-orange-600" />
                  </div>
                </div>
                <div className="mt-4">
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-orange-600 h-2 rounded-full" 
                      style={{ width: `${(stats.system.storageUsed / stats.system.storageLimit) * 100}%` }}
                    ></div>
                  </div>
                </div>
                <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  {formatBytes(stats.system.storageUsed)} of {formatBytes(stats.system.storageLimit)}
                </div>
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <QuickActionCard
              title="User Management"
              description="Manage users, roles, and permissions"
              icon={<Users className="w-6 h-6" />}
              href="/admin/users"
              color="blue"
            />
            
            <QuickActionCard
              title="Analytics"
              description="View detailed analytics and reports"
              icon={<TrendingUp className="w-6 h-6" />}
              href="/admin/analytics"
              color="green"
            />
            
            <QuickActionCard
              title="System Settings"
              description="Configure system settings and features"
              icon={<Settings className="w-6 h-6" />}
              href="/admin/settings"
              color="purple"
            />
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
};

interface QuickActionCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  color: 'blue' | 'green' | 'purple';
}

const QuickActionCard: React.FC<QuickActionCardProps> = ({ title, description, icon, href, color }) => {
  const colorClasses = {
    blue: 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 hover:bg-blue-200 dark:hover:bg-blue-900/30',
    green: 'bg-green-100 dark:bg-green-900/20 text-green-600 hover:bg-green-200 dark:hover:bg-green-900/30',
    purple: 'bg-purple-100 dark:bg-purple-900/20 text-purple-600 hover:bg-purple-200 dark:hover:bg-purple-900/30'
  };

  return (
    <a
      href={href}
      className="block bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow"
    >
      <div className="flex items-center gap-4">
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center transition-colors ${colorClasses[color]}`}>
          {icon}
        </div>
        <div>
          <h3 className="font-semibold text-gray-900 dark:text-white">{title}</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{description}</p>
        </div>
      </div>
    </a>
  );
};

export default AdminDashboard;
