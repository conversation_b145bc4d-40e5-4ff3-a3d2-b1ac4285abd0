import {promptExtra, ToolInfo} from "./prompt";
import {Messages} from "./action"
import {handleBuilderMode} from "./handlers/builderHandler"
import {handleChatMode} from "./handlers/chatHandler"
import { modelConfig } from "../model/config";
import { NextRequest } from 'next/server';
import { requireAuth, UserRole, Permission, canPerformAction } from '@/lib/auth';

enum ChatMode {
    Chat = "chat",
    Builder = "builder",
}



interface ChatRequest {
    messages: Messages;
    model: string;
    mode: ChatMode;
    otherConfig: promptExtra
    tools?: ToolInfo[]
}

export async function POST(request: NextRequest) {
    try {
        // Check if user has permission to use AI assistant
        const authResult = await requireAuth(UserRole.USER, [Permission.AI_ASSISTANT])(request);

        if (authResult instanceof Response) {
            return authResult; // Return error response for unauthorized access
        }

        const { user } = authResult;

        const {
            messages,
            model,
            mode = ChatMode.Builder,
            otherConfig,
            tools,
        } = (await request.json()) as ChatRequest;

        // Check if user can make AI calls based on usage limits
        // Note: You would need to fetch current usage from database
        const mockCurrentUsage = {
            projectsThisMonth: 0,
            aiCallsThisMonth: 45, // Example current usage
            storageUsed: 0,
            resetDate: new Date()
        };

        if (!canPerformAction(user.role, mockCurrentUsage, 'aiCall')) {
            return new Response(JSON.stringify({
                error: 'AI call limit exceeded for your plan',
                upgradeRequired: user.role === UserRole.USER
            }), {
                status: 429,
                headers: { 'Content-Type': 'application/json' }
            });
        }

        const userId = user.userId;
        const result =
            mode === ChatMode.Chat
                ? await handleChatMode(messages, model, userId, tools)
                : await handleBuilderMode(messages, model, userId, otherConfig, tools)

        console.log(result, 'result');

        // TODO: Increment AI call usage in database
        // await incrementAICallUsage(userId);

        return result
    } catch (error) {
        console.log(error, "error");

        if (error instanceof Error && error.message?.includes("API key")) {
            return new Response("Invalid or missing API key", {status: 401});
        }
        return new Response(String(error.message), {status: 500});
    }
}

