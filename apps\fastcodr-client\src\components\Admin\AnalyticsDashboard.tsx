import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  Activity,
  Globe,
  Smartphone,
  Monitor,
  Download,
  Calendar,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  RefreshCw
} from 'lucide-react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { Permission, UserRole } from '@/types/auth';
import useUserStore from '@/stores/userSlice';
import { toast } from 'react-hot-toast';

interface AnalyticsData {
  overview: {
    totalUsers: number;
    activeUsers: number;
    totalProjects: number;
    totalRevenue: number;
    conversionRate: number;
    churnRate: number;
  };
  userGrowth: {
    date: string;
    newUsers: number;
    activeUsers: number;
    churnedUsers: number;
  }[];
  revenueGrowth: {
    date: string;
    revenue: number;
    subscriptions: number;
    mrr: number;
  }[];
  featureUsage: {
    feature: string;
    usage: number;
    users: number;
    growth: number;
  }[];
  geographics: {
    country: string;
    users: number;
    revenue: number;
    percentage: number;
  }[];
  devices: {
    device: string;
    users: number;
    percentage: number;
  }[];
  topProjects: {
    id: string;
    name: string;
    author: string;
    views: number;
    forks: number;
    stars: number;
  }[];
}

const AnalyticsDashboard: React.FC = () => {
  const { token } = useUserStore();
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d');
  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'revenue' | 'features'>('overview');

  const fetchAnalytics = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`${process.env.APP_BASE_URL}/api/admin/analytics?timeRange=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch analytics');
      }

      const data = await response.json();
      setAnalytics(data.data);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      toast.error('Failed to load analytics data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) {
      return <TrendingUp className="w-4 h-4 text-green-500" />;
    } else if (growth < 0) {
      return <TrendingDown className="w-4 h-4 text-red-500" />;
    }
    return <Activity className="w-4 h-4 text-gray-500" />;
  };

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return 'text-green-600';
    if (growth < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute requiredRole={UserRole.ADMIN} requiredPermissions={[Permission.ADMIN_DASHBOARD]}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  Analytics Dashboard
                </h1>
                <p className="mt-2 text-gray-600 dark:text-gray-400">
                  Detailed insights and performance metrics
                </p>
              </div>
              
              <div className="flex items-center gap-4">
                <select
                  value={timeRange}
                  onChange={(e) => setTimeRange(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="7d">Last 7 days</option>
                  <option value="30d">Last 30 days</option>
                  <option value="90d">Last 90 days</option>
                  <option value="1y">Last year</option>
                </select>
                
                <button
                  onClick={fetchAnalytics}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
                >
                  <RefreshCw className="w-4 h-4" />
                  Refresh
                </button>
                
                <button className="flex items-center gap-2 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors">
                  <Download className="w-4 h-4" />
                  Export
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Tabs */}
          <div className="mb-8">
            <div className="border-b border-gray-200 dark:border-gray-700">
              <nav className="-mb-px flex space-x-8">
                {[
                  { id: 'overview', name: 'Overview', icon: BarChart3 },
                  { id: 'users', name: 'Users', icon: Users },
                  { id: 'revenue', name: 'Revenue', icon: DollarSign },
                  { id: 'features', name: 'Features', icon: Activity }
                ].map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id as any)}
                      className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                        activeTab === tab.id
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <Icon className="w-4 h-4" />
                      {tab.name}
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {analytics && (
            <>
              {/* Overview Tab */}
              {activeTab === 'overview' && (
                <div className="space-y-8">
                  {/* Key Metrics */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <MetricCard
                      title="Total Users"
                      value={analytics.overview.totalUsers.toLocaleString()}
                      icon={<Users className="w-6 h-6 text-blue-600" />}
                      growth={12.5}
                      subtitle={`${analytics.overview.activeUsers} active`}
                    />
                    
                    <MetricCard
                      title="Total Projects"
                      value={analytics.overview.totalProjects.toLocaleString()}
                      icon={<BarChart3 className="w-6 h-6 text-green-600" />}
                      growth={8.3}
                      subtitle="Projects created"
                    />
                    
                    <MetricCard
                      title="Total Revenue"
                      value={formatCurrency(analytics.overview.totalRevenue)}
                      icon={<DollarSign className="w-6 h-6 text-purple-600" />}
                      growth={15.2}
                      subtitle="This month"
                    />
                    
                    <MetricCard
                      title="Conversion Rate"
                      value={formatPercentage(analytics.overview.conversionRate)}
                      icon={<TrendingUp className="w-6 h-6 text-orange-600" />}
                      growth={2.1}
                      subtitle="Free to paid"
                    />
                  </div>

                  {/* Charts Placeholder */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        User Growth
                      </h3>
                      <div className="h-64 flex items-center justify-center text-gray-500">
                        <div className="text-center">
                          <LineChart className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                          <p>User growth chart would go here</p>
                          <p className="text-sm">Integration with Chart.js or similar</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        Revenue Growth
                      </h3>
                      <div className="h-64 flex items-center justify-center text-gray-500">
                        <div className="text-center">
                          <BarChart3 className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                          <p>Revenue chart would go here</p>
                          <p className="text-sm">Integration with Chart.js or similar</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Feature Usage Tab */}
              {activeTab === 'features' && (
                <div className="space-y-6">
                  <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Feature Usage Statistics
                      </h3>
                    </div>
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead className="bg-gray-50 dark:bg-gray-700">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                              Feature
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                              Usage Count
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                              Active Users
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                              Growth
                            </th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                          {analytics.featureUsage.map((feature) => (
                            <tr key={feature.feature} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                  {feature.feature}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-900 dark:text-white">
                                  {feature.usage.toLocaleString()}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-900 dark:text-white">
                                  {feature.users.toLocaleString()}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className={`flex items-center gap-1 text-sm ${getGrowthColor(feature.growth)}`}>
                                  {getGrowthIcon(feature.growth)}
                                  {feature.growth > 0 ? '+' : ''}{feature.growth}%
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {/* Geographic Distribution */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        Geographic Distribution
                      </h3>
                      <div className="space-y-3">
                        {analytics.geographics.map((geo) => (
                          <div key={geo.country} className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Globe className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-900 dark:text-white">{geo.country}</span>
                            </div>
                            <div className="text-right">
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                {geo.users} users
                              </div>
                              <div className="text-xs text-gray-500">
                                {formatCurrency(geo.revenue)}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        Device Distribution
                      </h3>
                      <div className="space-y-3">
                        {analytics.devices.map((device) => {
                          const Icon = device.device === 'Desktop' ? Monitor : device.device === 'Mobile' ? Smartphone : Monitor;
                          return (
                            <div key={device.device} className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <Icon className="w-4 h-4 text-gray-400" />
                                <span className="text-sm text-gray-900 dark:text-white">{device.device}</span>
                              </div>
                              <div className="text-right">
                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                  {device.users} users
                                </div>
                                <div className="text-xs text-gray-500">
                                  {formatPercentage(device.percentage)}
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </ProtectedRoute>
  );
};

interface MetricCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  growth: number;
  subtitle?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, icon, growth, subtitle }) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <p className="text-3xl font-bold text-gray-900 dark:text-white">{value}</p>
        </div>
        <div className="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
          {icon}
        </div>
      </div>
      <div className="mt-4 flex items-center justify-between">
        <div className={`flex items-center gap-1 text-sm ${growth > 0 ? 'text-green-600' : growth < 0 ? 'text-red-600' : 'text-gray-600'}`}>
          {growth > 0 ? <TrendingUp className="w-4 h-4" /> : growth < 0 ? <TrendingDown className="w-4 h-4" /> : <Activity className="w-4 h-4" />}
          {growth > 0 ? '+' : ''}{growth}%
        </div>
        {subtitle && (
          <span className="text-sm text-gray-500 dark:text-gray-400">{subtitle}</span>
        )}
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
