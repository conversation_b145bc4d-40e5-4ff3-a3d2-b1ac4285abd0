import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, UserRole, Permission } from '@/lib/auth';
import { PROJECT_TEMPLATES, ProjectTemplate } from '@/lib/models/Project';

// GET /api/projects/templates - Get available project templates
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(UserRole.USER)(request);
    
    if (authResult instanceof Response) {
      return authResult;
    }

    const { user } = authResult;
    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const category = searchParams.get('category');
    const framework = searchParams.get('framework');
    const language = searchParams.get('language');
    const includePremium = user.role !== UserRole.USER; // Only subscribers and admins see premium templates

    // Filter templates
    let templates = PROJECT_TEMPLATES.filter(template => {
      // Filter out premium templates for free users
      if (template.isPremium && !includePremium) {
        return false;
      }
      
      // Apply filters
      if (category && template.category.toLowerCase() !== category.toLowerCase()) {
        return false;
      }
      
      if (framework && template.framework.toLowerCase() !== framework.toLowerCase()) {
        return false;
      }
      
      if (language && template.language.toLowerCase() !== language.toLowerCase()) {
        return false;
      }
      
      return true;
    });

    // Add additional templates based on user role
    const additionalTemplates: ProjectTemplate[] = [];

    if (includePremium) {
      additionalTemplates.push(
        {
          id: 'express-api',
          name: 'Express.js API',
          description: 'RESTful API server with Express.js and TypeScript',
          category: 'Backend',
          framework: 'express',
          language: 'typescript',
          thumbnail: '/templates/express.png',
          isPremium: true,
          tags: ['express', 'nodejs', 'api', 'typescript'],
          createdBy: 'system',
          usageCount: 45,
          rating: 4.8,
          createdAt: new Date(),
          files: [
            {
              id: 'server-ts',
              name: 'server.ts',
              content: `import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { config } from './config';
import { errorHandler } from './middleware/errorHandler';
import { apiRoutes } from './routes';

const app = express();

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api', apiRoutes);

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Error handling
app.use(errorHandler);

const PORT = config.port || 3000;

app.listen(PORT, () => {
  console.log(\`Server running on port \${PORT}\`);
});`,
              language: 'typescript',
              path: '/server.ts',
              size: 0,
              lastModified: new Date(),
              isDirectory: false
            }
          ]
        },
        {
          id: 'flutter-app',
          name: 'Flutter Mobile App',
          description: 'Cross-platform mobile app with Flutter',
          category: 'Mobile',
          framework: 'flutter',
          language: 'dart',
          thumbnail: '/templates/flutter.png',
          isPremium: true,
          tags: ['flutter', 'mobile', 'dart', 'cross-platform'],
          createdBy: 'system',
          usageCount: 28,
          rating: 4.9,
          createdAt: new Date(),
          files: []
        }
      );
    }

    templates = [...templates, ...additionalTemplates];

    // Group templates by category
    const categorizedTemplates = templates.reduce((acc, template) => {
      const category = template.category;
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(template);
      return acc;
    }, {} as Record<string, ProjectTemplate[]>);

    // Get popular templates
    const popularTemplates = templates
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, 6);

    // Get recent templates
    const recentTemplates = templates
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, 6);

    return NextResponse.json({
      success: true,
      data: {
        templates,
        categorized: categorizedTemplates,
        popular: popularTemplates,
        recent: recentTemplates,
        stats: {
          total: templates.length,
          premium: templates.filter(t => t.isPremium).length,
          free: templates.filter(t => !t.isPremium).length,
          categories: Object.keys(categorizedTemplates).length
        }
      }
    });

  } catch (error) {
    console.error('Error fetching templates:', error);
    return NextResponse.json(
      { error: 'Failed to fetch templates' },
      { status: 500 }
    );
  }
}

// POST /api/projects/templates - Create project from template
export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(UserRole.USER, [Permission.SAVE_PROJECT])(request);
    
    if (authResult instanceof Response) {
      return authResult;
    }

    const { user } = authResult;
    const { templateId, projectName, projectDescription } = await request.json();

    if (!templateId || !projectName) {
      return NextResponse.json(
        { error: 'Template ID and project name are required' },
        { status: 400 }
      );
    }

    // Find template
    const template = PROJECT_TEMPLATES.find(t => t.id === templateId);
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Check if user can access premium templates
    if (template.isPremium && user.role === UserRole.USER) {
      return NextResponse.json(
        { error: 'Premium template requires subscription' },
        { status: 403 }
      );
    }

    // Create project from template
    const projectId = `proj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const newProject = {
      id: projectId,
      name: projectName,
      description: projectDescription || template.description,
      userId: user.userId,
      files: template.files.map(file => ({
        ...file,
        id: `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        lastModified: new Date()
      })),
      settings: {
        theme: 'dark' as const,
        fontSize: 14,
        tabSize: 2,
        wordWrap: true,
        minimap: true,
        autoSave: true,
        preferredEditor: 'monaco' as const
      },
      metadata: {
        framework: template.framework,
        language: template.language,
        template: template.id,
        dependencies: [],
        buildCommand: 'npm run build',
        startCommand: 'npm start'
      },
      tags: [...template.tags],
      isPublic: false,
      isTemplate: false,
      collaborators: [],
      viewCount: 0,
      forkCount: 0,
      starCount: 0,
      status: 'draft' as const,
      version: '0.1.0',
      isDraft: true,
      totalSize: 0,
      fileCount: template.files.length,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // TODO: Save to database
    // await saveProjectToDatabase(newProject);

    // TODO: Increment template usage count
    // await incrementTemplateUsage(templateId);

    return NextResponse.json({
      success: true,
      data: newProject,
      message: `Project created from ${template.name} template`
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating project from template:', error);
    return NextResponse.json(
      { error: 'Failed to create project from template' },
      { status: 500 }
    );
  }
}
