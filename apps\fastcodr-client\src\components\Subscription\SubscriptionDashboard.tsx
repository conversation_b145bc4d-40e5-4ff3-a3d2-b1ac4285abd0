import React, { useState, useEffect } from 'react';
import {
  Crown,
  Calendar,
  CreditCard,
  Download,
  AlertTriangle,
  CheckCircle,
  XCircle,
  TrendingUp,
  BarChart3,
  <PERSON>tings,
  RefreshCw,
  ExternalLink
} from 'lucide-react';
import useUserStore from '@/stores/userSlice';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { Permission, UserRole } from '@/types/auth';
import { toast } from 'react-hot-toast';
import { formatDistanceToNow } from 'date-fns';

interface SubscriptionData {
  subscription: {
    id: string;
    planId: string;
    status: string;
    currentPeriodStart: Date;
    currentPeriodEnd: Date;
    cancelAtPeriodEnd: boolean;
    amount: number;
    currency: string;
  } | null;
  plan: {
    name: string;
    description: string;
    price: number;
    currency: string;
    interval: string;
    features: string[];
    limits: {
      projects: number;
      aiCalls: number;
      storage: number;
      collaborators: number;
    };
  } | null;
  usage: {
    projects: { used: number; limit: number; percentage: number };
    aiCalls: { used: number; limit: number; percentage: number };
    storage: { used: number; limit: number; percentage: number };
  } | null;
  billingInfo: {
    nextBillingDate: Date;
    amount: string;
    status: string;
    cancelAtPeriodEnd: boolean;
  } | null;
}

const SubscriptionDashboard: React.FC = () => {
  const { user, token } = useUserStore();
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    fetchSubscriptionData();
  }, []);

  const fetchSubscriptionData = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`${process.env.APP_BASE_URL}/api/subscriptions`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch subscription data');
      }

      const data = await response.json();
      setSubscriptionData(data.data);
    } catch (error) {
      console.error('Error fetching subscription data:', error);
      toast.error('Failed to load subscription data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!window.confirm('Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your billing period.')) {
      return;
    }

    setIsProcessing(true);
    try {
      const response = await fetch(`${process.env.APP_BASE_URL}/api/subscriptions`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action: 'cancel' })
      });

      if (!response.ok) {
        throw new Error('Failed to cancel subscription');
      }

      toast.success('Subscription cancelled successfully');
      fetchSubscriptionData();
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      toast.error('Failed to cancel subscription');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReactivateSubscription = async () => {
    setIsProcessing(true);
    try {
      const response = await fetch(`${process.env.APP_BASE_URL}/api/subscriptions`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action: 'reactivate' })
      });

      if (!response.ok) {
        throw new Error('Failed to reactivate subscription');
      }

      toast.success('Subscription reactivated successfully');
      fetchSubscriptionData();
    } catch (error) {
      console.error('Error reactivating subscription:', error);
      toast.error('Failed to reactivate subscription');
    } finally {
      setIsProcessing(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', icon: CheckCircle, text: 'Active' },
      cancelled: { color: 'bg-red-100 text-red-800', icon: XCircle, text: 'Cancelled' },
      expired: { color: 'bg-gray-100 text-gray-800', icon: XCircle, text: 'Expired' },
      past_due: { color: 'bg-yellow-100 text-yellow-800', icon: AlertTriangle, text: 'Past Due' },
      trialing: { color: 'bg-blue-100 text-blue-800', icon: Crown, text: 'Trial' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${config.color}`}>
        <Icon className="w-4 h-4" />
        {config.text}
      </span>
    );
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading subscription details...</p>
        </div>
      </div>
    );
  }

  if (!subscriptionData?.subscription) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <Crown className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              No Active Subscription
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mb-8">
              You're currently on the free plan. Upgrade to unlock premium features.
            </p>
            <a
              href="/subscription/plans"
              className="inline-flex items-center gap-2 px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
            >
              <Crown className="w-5 h-5" />
              View Plans
            </a>
          </div>
        </div>
      </div>
    );
  }

  const { subscription, plan, usage, billingInfo } = subscriptionData;

  return (
    <ProtectedRoute requiredPermissions={[Permission.VIEW_EDITOR]}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Subscription Dashboard
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Manage your FastCodr subscription and usage
              </p>
            </div>
            
            <button
              onClick={fetchSubscriptionData}
              className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              Refresh
            </button>
          </div>

          {/* Subscription Overview */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            {/* Current Plan */}
            <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Current Plan
                </h2>
                {getStatusBadge(subscription.status)}
              </div>
              
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <Crown className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    {plan?.name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    {plan?.description}
                  </p>
                </div>
              </div>

              {subscription.cancelAtPeriodEnd && (
                <div className="mb-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5 text-yellow-600" />
                    <span className="font-medium text-yellow-800 dark:text-yellow-200">
                      Subscription Cancelled
                    </span>
                  </div>
                  <p className="text-yellow-700 dark:text-yellow-300 text-sm mt-1">
                    Your subscription will end on {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
                  </p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Price:</span>
                  <span className="font-medium text-gray-900 dark:text-white ml-2">
                    ${subscription.amount}/{plan?.interval}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Next billing:</span>
                  <span className="font-medium text-gray-900 dark:text-white ml-2">
                    {formatDistanceToNow(new Date(subscription.currentPeriodEnd), { addSuffix: true })}
                  </span>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Quick Actions
              </h2>
              
              <div className="space-y-3">
                <a
                  href="/subscription/plans"
                  className="w-full flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
                >
                  <TrendingUp className="w-4 h-4" />
                  Upgrade Plan
                </a>
                
                <button className="w-full flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                  <CreditCard className="w-4 h-4" />
                  Update Payment
                </button>
                
                <button className="w-full flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                  <Download className="w-4 h-4" />
                  Download Invoice
                </button>
                
                {subscription.cancelAtPeriodEnd ? (
                  <button
                    onClick={handleReactivateSubscription}
                    disabled={isProcessing}
                    className="w-full flex items-center gap-2 px-4 py-2 bg-green-500 hover:bg-green-600 disabled:bg-gray-300 text-white rounded-lg transition-colors"
                  >
                    <CheckCircle className="w-4 h-4" />
                    Reactivate
                  </button>
                ) : (
                  <button
                    onClick={handleCancelSubscription}
                    disabled={isProcessing}
                    className="w-full flex items-center gap-2 px-4 py-2 bg-red-500 hover:bg-red-600 disabled:bg-gray-300 text-white rounded-lg transition-colors"
                  >
                    <XCircle className="w-4 h-4" />
                    Cancel Plan
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Usage Statistics */}
          {usage && (
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                Usage Statistics
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <UsageCard
                  title="Projects"
                  used={usage.projects.used}
                  limit={usage.projects.limit}
                  percentage={usage.projects.percentage}
                  icon={<BarChart3 className="w-5 h-5" />}
                />
                
                <UsageCard
                  title="AI Calls"
                  used={usage.aiCalls.used}
                  limit={usage.aiCalls.limit}
                  percentage={usage.aiCalls.percentage}
                  icon={<Crown className="w-5 h-5" />}
                />
                
                <UsageCard
                  title="Storage"
                  used={usage.storage.used}
                  limit={usage.storage.limit}
                  percentage={usage.storage.percentage}
                  icon={<Settings className="w-5 h-5" />}
                  formatter={formatBytes}
                />
              </div>
            </div>
          )}

          {/* Billing History */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Billing History
              </h2>
              <button className="flex items-center gap-2 text-blue-600 hover:text-blue-700 text-sm">
                <ExternalLink className="w-4 h-4" />
                View All
              </button>
            </div>
            
            <div className="space-y-4">
              <BillingHistoryItem
                date={new Date()}
                amount={subscription.amount}
                currency={subscription.currency}
                status="paid"
                description={`${plan?.name} subscription`}
              />
              <BillingHistoryItem
                date={new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)}
                amount={subscription.amount}
                currency={subscription.currency}
                status="paid"
                description={`${plan?.name} subscription`}
              />
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
};

interface UsageCardProps {
  title: string;
  used: number;
  limit: number;
  percentage: number;
  icon: React.ReactNode;
  formatter?: (value: number) => string;
}

const UsageCard: React.FC<UsageCardProps> = ({ title, used, limit, percentage, icon, formatter }) => {
  const formatValue = formatter || ((value: number) => value.toString());
  const isUnlimited = limit === -1;
  
  return (
    <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
      <div className="flex items-center gap-3 mb-3">
        <div className="text-blue-500">
          {icon}
        </div>
        <h3 className="font-medium text-gray-900 dark:text-white">
          {title}
        </h3>
      </div>
      
      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">
            {formatValue(used)} {isUnlimited ? '' : `of ${formatValue(limit)}`}
          </span>
          {!isUnlimited && (
            <span className="font-medium text-gray-900 dark:text-white">
              {percentage.toFixed(1)}%
            </span>
          )}
        </div>
        
        {!isUnlimited && (
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className={`h-2 rounded-full ${
                percentage > 90 ? 'bg-red-500' : 
                percentage > 70 ? 'bg-yellow-500' : 
                'bg-green-500'
              }`}
              style={{ width: `${Math.min(percentage, 100)}%` }}
            />
          </div>
        )}
      </div>
    </div>
  );
};

interface BillingHistoryItemProps {
  date: Date;
  amount: number;
  currency: string;
  status: string;
  description: string;
}

const BillingHistoryItem: React.FC<BillingHistoryItemProps> = ({ 
  date, 
  amount, 
  currency, 
  status, 
  description 
}) => {
  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  return (
    <div className="flex items-center justify-between py-3 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
      <div>
        <p className="font-medium text-gray-900 dark:text-white">
          {description}
        </p>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {date.toLocaleDateString()}
        </p>
      </div>
      
      <div className="text-right">
        <p className="font-medium text-gray-900 dark:text-white">
          {formatCurrency(amount, currency)}
        </p>
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          status === 'paid' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {status}
        </span>
      </div>
    </div>
  );
};

export default SubscriptionDashboard;
