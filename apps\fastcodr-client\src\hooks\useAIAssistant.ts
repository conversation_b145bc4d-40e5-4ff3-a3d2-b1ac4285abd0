import { useState, useCallback } from 'react';
import { useChat } from 'ai/react';
import useUserStore from '@/stores/userSlice';
import { Permission } from '@/types/auth';
import { toast } from 'react-hot-toast';

export interface AIAssistantOptions {
  model?: string;
  mode?: 'chat' | 'builder';
  onError?: (error: Error) => void;
  onSuccess?: (response: string) => void;
}

export interface AIPromptTemplate {
  id: string;
  name: string;
  description: string;
  prompt: string;
  category: 'code' | 'debug' | 'explain' | 'optimize' | 'generate';
}

export const AI_PROMPT_TEMPLATES: AIPromptTemplate[] = [
  {
    id: 'explain-code',
    name: 'Explain Code',
    description: 'Get a detailed explanation of how code works',
    prompt: 'Please explain what this code does, how it works, and any important details:',
    category: 'explain'
  },
  {
    id: 'debug-code',
    name: 'Debug Code',
    description: 'Find and fix bugs in your code',
    prompt: 'Please analyze this code for potential bugs, errors, or issues and suggest fixes:',
    category: 'debug'
  },
  {
    id: 'optimize-code',
    name: 'Optimize Code',
    description: 'Improve code performance and efficiency',
    prompt: 'Please optimize this code for better performance and efficiency:',
    category: 'optimize'
  },
  {
    id: 'refactor-code',
    name: 'Refactor Code',
    description: 'Clean up and improve code structure',
    prompt: 'Please refactor this code to make it cleaner, more maintainable, and follow best practices:',
    category: 'code'
  },
  {
    id: 'generate-tests',
    name: 'Generate Tests',
    description: 'Create unit tests for your code',
    prompt: 'Please generate comprehensive unit tests for this code:',
    category: 'generate'
  },
  {
    id: 'add-comments',
    name: 'Add Comments',
    description: 'Add helpful comments to your code',
    prompt: 'Please add clear and helpful comments to this code:',
    category: 'code'
  },
  {
    id: 'convert-language',
    name: 'Convert Language',
    description: 'Convert code to another programming language',
    prompt: 'Please convert this code to [TARGET_LANGUAGE]:',
    category: 'generate'
  },
  {
    id: 'security-review',
    name: 'Security Review',
    description: 'Check code for security vulnerabilities',
    prompt: 'Please review this code for security vulnerabilities and suggest improvements:',
    category: 'debug'
  }
];

export const useAIAssistant = (options: AIAssistantOptions = {}) => {
  const { user, token, hasPermission, getRemainingUsage } = useUserStore();
  const [isProcessing, setIsProcessing] = useState(false);
  const remainingUsage = getRemainingUsage();

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    error,
    setMessages,
    append
  } = useChat({
    api: `${process.env.APP_BASE_URL}/api/chat`,
    headers: {
      ...(token && { Authorization: `Bearer ${token}` }),
    },
    body: {
      model: options.model || 'claude-3-5-sonnet',
      mode: options.mode || 'chat',
      otherConfig: {
        extra: {
          isBackEnd: false,
          backendLanguage: 'javascript'
        }
      }
    },
    onError: (error) => {
      console.error('AI Assistant error:', error);
      setIsProcessing(false);
      
      if (error.message.includes('AI call limit exceeded')) {
        toast.error('AI call limit exceeded. Upgrade to Premium for unlimited access!');
      } else if (error.message.includes('Authentication required')) {
        toast.error('Please log in to use the AI Assistant');
      } else {
        toast.error('AI Assistant error: ' + error.message);
      }
      
      options.onError?.(error);
    },
    onFinish: (message) => {
      setIsProcessing(false);
      options.onSuccess?.(message.content);
    }
  });

  // Check if user can use AI assistant
  const canUseAI = useCallback(() => {
    if (!user) {
      toast.error('Please log in to use the AI Assistant');
      return false;
    }

    if (!hasPermission(Permission.AI_ASSISTANT)) {
      toast.error('AI Assistant is a premium feature. Upgrade to access it!');
      return false;
    }

    if (remainingUsage.aiCalls === 0) {
      toast.error('AI call limit reached. Upgrade to Premium for unlimited access!');
      return false;
    }

    return true;
  }, [user, hasPermission, remainingUsage.aiCalls]);

  // Send a prompt to AI
  const sendPrompt = useCallback(async (prompt: string, code?: string) => {
    if (!canUseAI()) return null;

    setIsProcessing(true);
    
    const fullPrompt = code ? `${prompt}\n\nCode:\n\`\`\`\n${code}\n\`\`\`` : prompt;
    
    try {
      await append({
        role: 'user',
        content: fullPrompt
      });
    } catch (error) {
      setIsProcessing(false);
      throw error;
    }
  }, [canUseAI, append]);

  // Use a predefined template
  const useTemplate = useCallback(async (templateId: string, code?: string, variables?: Record<string, string>) => {
    const template = AI_PROMPT_TEMPLATES.find(t => t.id === templateId);
    if (!template) {
      throw new Error(`Template ${templateId} not found`);
    }

    let prompt = template.prompt;
    
    // Replace variables in prompt
    if (variables) {
      Object.entries(variables).forEach(([key, value]) => {
        prompt = prompt.replace(`[${key.toUpperCase()}]`, value);
      });
    }

    return sendPrompt(prompt, code);
  }, [sendPrompt]);

  // Quick actions for common tasks
  const explainCode = useCallback((code: string) => {
    return useTemplate('explain-code', code);
  }, [useTemplate]);

  const debugCode = useCallback((code: string) => {
    return useTemplate('debug-code', code);
  }, [useTemplate]);

  const optimizeCode = useCallback((code: string) => {
    return useTemplate('optimize-code', code);
  }, [useTemplate]);

  const refactorCode = useCallback((code: string) => {
    return useTemplate('refactor-code', code);
  }, [useTemplate]);

  const generateTests = useCallback((code: string) => {
    return useTemplate('generate-tests', code);
  }, [useTemplate]);

  const addComments = useCallback((code: string) => {
    return useTemplate('add-comments', code);
  }, [useTemplate]);

  const convertLanguage = useCallback((code: string, targetLanguage: string) => {
    return useTemplate('convert-language', code, { TARGET_LANGUAGE: targetLanguage });
  }, [useTemplate]);

  const securityReview = useCallback((code: string) => {
    return useTemplate('security-review', code);
  }, [useTemplate]);

  // Get the latest AI response
  const getLatestResponse = useCallback(() => {
    const lastMessage = messages[messages.length - 1];
    return lastMessage?.role === 'assistant' ? lastMessage.content : null;
  }, [messages]);

  // Extract code blocks from AI response
  const extractCodeBlocks = useCallback((response?: string) => {
    const content = response || getLatestResponse();
    if (!content) return [];

    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    const codeBlocks = [];
    let match;

    while ((match = codeBlockRegex.exec(content)) !== null) {
      codeBlocks.push({
        language: match[1] || 'text',
        code: match[2].trim()
      });
    }

    return codeBlocks;
  }, [getLatestResponse]);

  // Clear conversation
  const clearConversation = useCallback(() => {
    setMessages([]);
  }, [setMessages]);

  return {
    // Chat interface
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading: isLoading || isProcessing,
    error,
    
    // AI capabilities
    canUseAI: canUseAI(),
    remainingCalls: remainingUsage.aiCalls,
    
    // Core functions
    sendPrompt,
    useTemplate,
    
    // Quick actions
    explainCode,
    debugCode,
    optimizeCode,
    refactorCode,
    generateTests,
    addComments,
    convertLanguage,
    securityReview,
    
    // Utilities
    getLatestResponse,
    extractCodeBlocks,
    clearConversation,
    
    // Templates
    templates: AI_PROMPT_TEMPLATES
  };
};
