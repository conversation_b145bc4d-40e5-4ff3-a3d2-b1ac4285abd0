import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, UserRole, Permission } from '@/lib/auth';
import { 
  SUBSCRIPTION_PLANS, 
  getPlanById, 
  getActivePlans,
  Subscription,
  formatPrice 
} from '@/lib/models/Subscription';
import { 
  cashfreeService, 
  generateSubscriptionId, 
  generateCustomerId 
} from '@/lib/services/cashfree';

// GET /api/subscriptions - Get user's subscription and available plans
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(UserRole.USER)(request);
    
    if (authResult instanceof Response) {
      return authResult;
    }

    const { user } = authResult;

    // Mock current subscription - replace with database query
    const currentSubscription: Subscription | null = user.role === UserRole.SUBSCRIBER ? {
      id: 'sub_123',
      userId: user.userId,
      planId: 'premium_monthly',
      status: 'active',
      currentPeriodStart: new Date('2024-01-15'),
      currentPeriodEnd: new Date('2024-02-15'),
      cancelAtPeriodEnd: false,
      provider: 'cashfree',
      providerSubscriptionId: 'cf_sub_123',
      providerCustomerId: 'cf_customer_123',
      amount: 10,
      currency: 'USD',
      usage: {
        projects: 5,
        aiCalls: 150,
        storage: 250,
        resetDate: new Date('2024-02-01')
      },
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15')
    } : null;

    // Get available plans
    const availablePlans = getActivePlans();

    // Calculate usage and limits
    const currentPlan = currentSubscription ? getPlanById(currentSubscription.planId) : getPlanById('free');
    
    const usageInfo = currentSubscription && currentPlan ? {
      projects: {
        used: currentSubscription.usage.projects,
        limit: currentPlan.limits.projects,
        percentage: currentPlan.limits.projects === -1 ? 0 : (currentSubscription.usage.projects / currentPlan.limits.projects) * 100
      },
      aiCalls: {
        used: currentSubscription.usage.aiCalls,
        limit: currentPlan.limits.aiCalls,
        percentage: currentPlan.limits.aiCalls === -1 ? 0 : (currentSubscription.usage.aiCalls / currentPlan.limits.aiCalls) * 100
      },
      storage: {
        used: currentSubscription.usage.storage,
        limit: currentPlan.limits.storage,
        percentage: currentPlan.limits.storage === -1 ? 0 : (currentSubscription.usage.storage / currentPlan.limits.storage) * 100
      }
    } : null;

    return NextResponse.json({
      success: true,
      data: {
        currentSubscription,
        currentPlan,
        availablePlans,
        usage: usageInfo,
        billingInfo: currentSubscription ? {
          nextBillingDate: currentSubscription.currentPeriodEnd,
          amount: formatPrice(currentSubscription.amount, currentSubscription.currency),
          status: currentSubscription.status,
          cancelAtPeriodEnd: currentSubscription.cancelAtPeriodEnd
        } : null
      }
    });

  } catch (error) {
    console.error('Error fetching subscription data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscription data' },
      { status: 500 }
    );
  }
}

// POST /api/subscriptions - Create new subscription
export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(UserRole.USER)(request);
    
    if (authResult instanceof Response) {
      return authResult;
    }

    const { user } = authResult;
    const { planId, paymentMethodId } = await request.json();

    if (!planId) {
      return NextResponse.json(
        { error: 'Plan ID is required' },
        { status: 400 }
      );
    }

    // Get the selected plan
    const plan = getPlanById(planId);
    if (!plan || !plan.isActive) {
      return NextResponse.json(
        { error: 'Invalid or inactive plan' },
        { status: 400 }
      );
    }

    // Check if user already has an active subscription
    // TODO: Query database for existing subscription
    const existingSubscription = null; // Replace with actual query

    if (existingSubscription && existingSubscription.status === 'active') {
      return NextResponse.json(
        { error: 'User already has an active subscription' },
        { status: 400 }
      );
    }

    // Generate IDs
    const subscriptionId = generateSubscriptionId(user.userId);
    const customerId = generateCustomerId(user.userId);

    try {
      // Create customer in Cashfree if not exists
      await cashfreeService.createCustomer({
        customerId,
        customerEmail: user.email,
        customerPhone: user.phone || '+1234567890', // TODO: Get from user profile
        customerName: user.name || user.email
      });

      // Create subscription in Cashfree
      const cashfreeSubscription = await cashfreeService.createSubscription({
        subscriptionId,
        planId: plan.cashfreePlanId || planId,
        customerId,
        customerEmail: user.email,
        customerPhone: user.phone || '+1234567890',
        returnUrl: `${process.env.NEXT_PUBLIC_APP_URL}/subscription/success`,
        notifyUrl: `${process.env.NEXT_PUBLIC_APP_URL}/api/webhooks/cashfree`,
        metadata: {
          userId: user.userId,
          planId: planId
        }
      });

      // Create subscription record in database
      const newSubscription: Subscription = {
        id: subscriptionId,
        userId: user.userId,
        planId: planId,
        status: 'incomplete', // Will be updated via webhook
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(Date.now() + (plan.interval === 'yearly' ? 365 : 30) * 24 * 60 * 60 * 1000),
        cancelAtPeriodEnd: false,
        provider: 'cashfree',
        providerSubscriptionId: cashfreeSubscription.subscription_id,
        providerCustomerId: customerId,
        amount: plan.price,
        currency: plan.currency,
        usage: {
          projects: 0,
          aiCalls: 0,
          storage: 0,
          resetDate: new Date()
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // TODO: Save subscription to database
      // await saveSubscriptionToDatabase(newSubscription);

      return NextResponse.json({
        success: true,
        data: {
          subscription: newSubscription,
          paymentUrl: cashfreeSubscription.payment_link,
          subscriptionId: subscriptionId
        },
        message: 'Subscription created successfully'
      }, { status: 201 });

    } catch (cashfreeError) {
      console.error('Cashfree API error:', cashfreeError);
      return NextResponse.json(
        { error: 'Payment processing failed. Please try again.' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error creating subscription:', error);
    return NextResponse.json(
      { error: 'Failed to create subscription' },
      { status: 500 }
    );
  }
}

// PUT /api/subscriptions - Update subscription (upgrade/downgrade)
export async function PUT(request: NextRequest) {
  try {
    const authResult = await requireAuth(UserRole.USER)(request);
    
    if (authResult instanceof Response) {
      return authResult;
    }

    const { user } = authResult;
    const { action, planId } = await request.json();

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    // TODO: Get current subscription from database
    const currentSubscription = null; // Replace with actual query

    if (!currentSubscription) {
      return NextResponse.json(
        { error: 'No active subscription found' },
        { status: 404 }
      );
    }

    switch (action) {
      case 'cancel':
        try {
          // Cancel subscription in Cashfree
          await cashfreeService.cancelSubscription(currentSubscription.providerSubscriptionId);

          // Update subscription in database
          // TODO: Update subscription status to 'cancelled'
          
          return NextResponse.json({
            success: true,
            message: 'Subscription cancelled successfully'
          });
        } catch (error) {
          console.error('Error cancelling subscription:', error);
          return NextResponse.json(
            { error: 'Failed to cancel subscription' },
            { status: 500 }
          );
        }

      case 'upgrade':
      case 'downgrade':
        if (!planId) {
          return NextResponse.json(
            { error: 'Plan ID is required for plan changes' },
            { status: 400 }
          );
        }

        const newPlan = getPlanById(planId);
        if (!newPlan || !newPlan.isActive) {
          return NextResponse.json(
            { error: 'Invalid or inactive plan' },
            { status: 400 }
          );
        }

        // TODO: Implement plan change logic
        // This would involve:
        // 1. Calculating prorated amounts
        // 2. Creating new subscription in Cashfree
        // 3. Cancelling old subscription
        // 4. Updating database records

        return NextResponse.json({
          success: true,
          message: `Plan ${action} initiated successfully`
        });

      case 'reactivate':
        // TODO: Implement reactivation logic
        return NextResponse.json({
          success: true,
          message: 'Subscription reactivated successfully'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error updating subscription:', error);
    return NextResponse.json(
      { error: 'Failed to update subscription' },
      { status: 500 }
    );
  }
}
