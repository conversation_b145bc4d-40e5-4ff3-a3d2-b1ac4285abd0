import React, { forwardRef } from 'react';
import { useDrop } from 'react-dnd';
import { UIComponent } from '../VisualBuilder';
import RenderableComponent from './RenderableComponent';

interface DropCanvasProps {
  components: UIComponent[];
  selectedId: string | null;
  onSelect: (id: string) => void;
  onDrop: (type: string, parentId?: string) => void;
  onUpdate: (id: string, updates: Partial<UIComponent>) => void;
}

const DropCanvas = forwardRef<HTMLDivElement, DropCanvasProps>(({
  components,
  selectedId,
  onSelect,
  onDrop,
  onUpdate
}, ref) => {
  const [{ isOver }, drop] = useDrop(() => ({
    accept: 'component',
    drop: (item: { type: string }) => {
      onDrop(item.type);
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  }));

  return (
    <div
      ref={(node) => {
        drop(node);
        if (typeof ref === 'function') {
          ref(node);
        } else if (ref) {
          ref.current = node;
        }
      }}
      className={`
        min-h-[400px] p-4 relative
        ${isOver ? 'bg-blue-50 border-2 border-dashed border-blue-300' : 'bg-white'}
        ${components.length === 0 ? 'flex items-center justify-center' : ''}
      `}
    >
      {components.length === 0 ? (
        <div className="text-center text-gray-500">
          <div className="text-lg mb-2">🎨</div>
          <p className="text-sm">Drag components here to start building</p>
          <p className="text-xs text-gray-400 mt-1">or click components in the palette</p>
        </div>
      ) : (
        <div className="space-y-4">
          {components.map((component) => (
            <RenderableComponent
              key={component.id}
              component={component}
              isSelected={selectedId === component.id}
              onSelect={onSelect}
              onDrop={onDrop}
              onUpdate={onUpdate}
            />
          ))}
        </div>
      )}
    </div>
  );
});

DropCanvas.displayName = 'DropCanvas';

export default DropCanvas;
