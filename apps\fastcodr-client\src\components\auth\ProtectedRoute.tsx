import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import useUserStore from '@/stores/userSlice';
import { UserRole, Permission } from '@/types/auth';
import { AlertTriangle, <PERSON>, Crown } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: UserRole;
  requiredPermissions?: Permission[];
  fallbackPath?: string;
  showUpgradePrompt?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  requiredPermissions = [],
  fallbackPath = '/login',
  showUpgradePrompt = true
}) => {
  const { user, isAuthenticated, canAccessRoute, hasPermission, openLoginModal } = useUserStore();
  const location = useLocation();

  // Check if user is authenticated
  if (!isAuthenticated || !user) {
    return <Navigate to={fallbackPath} state={{ from: location }} replace />;
  }

  // Check role-based access
  if (requiredRole) {
    const roleHierarchy = [UserRole.USER, UserRole.SUBSCRIBER, UserRole.ADMIN];
    const userRoleIndex = roleHierarchy.indexOf(user.role);
    const requiredRoleIndex = roleHierarchy.indexOf(requiredRole);
    
    if (userRoleIndex < requiredRoleIndex) {
      if (showUpgradePrompt) {
        return <UpgradePrompt requiredRole={requiredRole} />;
      }
      return <Navigate to="/unauthorized" replace />;
    }
  }

  // Check specific permissions
  if (requiredPermissions.length > 0) {
    const hasAllPermissions = requiredPermissions.every(permission => 
      hasPermission(permission)
    );
    
    if (!hasAllPermissions) {
      if (showUpgradePrompt) {
        return <UpgradePrompt requiredPermissions={requiredPermissions} />;
      }
      return <Navigate to="/unauthorized" replace />;
    }
  }

  // Check route access
  if (!canAccessRoute(location.pathname)) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <>{children}</>;
};

interface UpgradePromptProps {
  requiredRole?: UserRole;
  requiredPermissions?: Permission[];
}

const UpgradePrompt: React.FC<UpgradePromptProps> = ({ requiredRole, requiredPermissions }) => {
  const { user } = useUserStore();

  const getUpgradeMessage = () => {
    if (requiredRole === UserRole.ADMIN) {
      return {
        title: "Administrator Access Required",
        description: "This feature is only available to administrators.",
        icon: <Lock className="w-16 h-16 text-red-500" />,
        showUpgrade: false
      };
    }
    
    if (requiredRole === UserRole.SUBSCRIBER || requiredPermissions?.some(p => p.includes('ai_assistant'))) {
      return {
        title: "Premium Feature",
        description: "Upgrade to FastCodr Premium to access this feature and unlock unlimited possibilities.",
        icon: <Crown className="w-16 h-16 text-yellow-500" />,
        showUpgrade: true
      };
    }

    return {
      title: "Access Restricted",
      description: "You don't have permission to access this feature.",
      icon: <AlertTriangle className="w-16 h-16 text-orange-500" />,
      showUpgrade: false
    };
  };

  const { title, description, icon, showUpgrade } = getUpgradeMessage();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
        <div className="flex justify-center mb-6">
          {icon}
        </div>
        
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          {title}
        </h2>
        
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          {description}
        </p>

        <div className="space-y-3">
          {showUpgrade && (
            <button
              onClick={() => window.location.href = '/subscribe'}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105"
            >
              Upgrade to Premium
            </button>
          )}
          
          <button
            onClick={() => window.history.back()}
            className="w-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Go Back
          </button>
        </div>

        {user && (
          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Current plan: <span className="font-medium">{user.role}</span>
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProtectedRoute;
