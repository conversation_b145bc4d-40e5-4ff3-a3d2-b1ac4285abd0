import React, { useState } from 'react';
import { 
  Save, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  Settings, 
  Loader2,
  Wifi,
  WifiOff
} from 'lucide-react';
import { useAutoSave, useAutoSaveSettings, useAutoSaveIndicator } from '@/hooks/useAutoSave';
import useProjectStore from '@/stores/projectSlice';
import { toast } from 'react-hot-toast';

interface ProjectSaveIndicatorProps {
  className?: string;
  showSettings?: boolean;
}

const ProjectSaveIndicator: React.FC<ProjectSaveIndicatorProps> = ({ 
  className = '',
  showSettings = true 
}) => {
  const { currentProject, isSaving } = useProjectStore();
  const { manualSave, getAutoSaveStatus } = useAutoSave();
  const { enabled, interval, setEnabled, setInterval } = useAutoSaveSettings();
  const { text, color, isAutoSaving, hasUnsavedChanges } = useAutoSaveIndicator();
  
  const [showSettingsPanel, setShowSettingsPanel] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Monitor online status
  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleManualSave = async () => {
    if (!currentProject) return;
    
    try {
      await manualSave();
      toast.success('Project saved successfully');
    } catch (error) {
      toast.error('Failed to save project');
    }
  };

  const getStatusIcon = () => {
    if (!isOnline) {
      return <WifiOff className="w-4 h-4 text-red-500" />;
    }
    
    if (isAutoSaving || isSaving) {
      return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
    }
    
    if (hasUnsavedChanges) {
      return <AlertCircle className="w-4 h-4 text-orange-500" />;
    }
    
    return <CheckCircle className="w-4 h-4 text-green-500" />;
  };

  if (!currentProject) {
    return null;
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Status Indicator */}
      <div className="flex items-center gap-2 px-3 py-1 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        {getStatusIcon()}
        
        <span className={`text-sm font-medium ${color}`}>
          {!isOnline ? 'Offline' : text}
        </span>
        
        {!isOnline && (
          <span className="text-xs text-gray-500">
            Changes saved locally
          </span>
        )}
      </div>

      {/* Manual Save Button */}
      <button
        onClick={handleManualSave}
        disabled={isSaving || isAutoSaving || !hasUnsavedChanges || !isOnline}
        className="flex items-center gap-1 px-3 py-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white text-sm rounded-lg transition-colors"
        title="Save project manually"
      >
        {isSaving ? (
          <Loader2 className="w-3 h-3 animate-spin" />
        ) : (
          <Save className="w-3 h-3" />
        )}
        Save
      </button>

      {/* Settings Button */}
      {showSettings && (
        <div className="relative">
          <button
            onClick={() => setShowSettingsPanel(!showSettingsPanel)}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
            title="Auto-save settings"
          >
            <Settings className="w-4 h-4 text-gray-500" />
          </button>

          {/* Settings Panel */}
          {showSettingsPanel && (
            <>
              {/* Backdrop */}
              <div 
                className="fixed inset-0 z-10" 
                onClick={() => setShowSettingsPanel(false)} 
              />
              
              {/* Panel */}
              <div className="absolute right-0 top-full mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-20">
                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-4">
                    Auto-save Settings
                  </h3>
                  
                  {/* Enable/Disable Auto-save */}
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Auto-save
                      </label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Automatically save changes
                      </p>
                    </div>
                    <button
                      onClick={() => setEnabled(!enabled)}
                      className={`
                        relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                        ${enabled ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'}
                      `}
                    >
                      <span
                        className={`
                          inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                          ${enabled ? 'translate-x-6' : 'translate-x-1'}
                        `}
                      />
                    </button>
                  </div>

                  {/* Auto-save Interval */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Save Interval
                    </label>
                    <select
                      value={interval}
                      onChange={(e) => setInterval(Number(e.target.value))}
                      disabled={!enabled}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed dark:bg-gray-700 dark:text-white"
                    >
                      <option value={10000}>10 seconds</option>
                      <option value={30000}>30 seconds</option>
                      <option value={60000}>1 minute</option>
                      <option value={120000}>2 minutes</option>
                      <option value={300000}>5 minutes</option>
                    </select>
                  </div>

                  {/* Status Info */}
                  <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-3">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                      Current Status
                    </h4>
                    <div className="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                      <div className="flex justify-between">
                        <span>Auto-save:</span>
                        <span className={enabled ? 'text-green-600' : 'text-red-600'}>
                          {enabled ? 'Enabled' : 'Disabled'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Interval:</span>
                        <span>{interval / 1000}s</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Connection:</span>
                        <span className={isOnline ? 'text-green-600' : 'text-red-600'}>
                          {isOnline ? 'Online' : 'Offline'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Unsaved changes:</span>
                        <span className={hasUnsavedChanges ? 'text-orange-600' : 'text-green-600'}>
                          {hasUnsavedChanges ? 'Yes' : 'No'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Help Text */}
                  <div className="mt-4 text-xs text-gray-500 dark:text-gray-400">
                    <p>
                      Auto-save helps prevent data loss by automatically saving your changes.
                      Manual saves are always available regardless of auto-save settings.
                    </p>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default ProjectSaveIndicator;
