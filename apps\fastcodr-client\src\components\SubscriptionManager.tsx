import React, { useState, useEffect } from 'react';
import useSubscriptionStore from '@/stores/subscriptionSlice';
import { 
  cancelSubscription, 
  verifySubscription, 
  getSupportEmail,
  getSubscriptionId 
} from '@/lib/cashfree';
import { 
  Crown, 
  Calendar, 
  CreditCard, 
  AlertTriangle, 
  CheckCircle, 
  Mail,
  RefreshCw 
} from 'lucide-react';

export function SubscriptionManager() {
  const { 
    isSubscribed, 
    subscriptionId, 
    subscriptionData, 
    verifySubscriptionStatus,
    clearSubscription,
    isLoading,
    error 
  } = useSubscriptionStore();

  const [cancelling, setCancelling] = useState(false);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);

  useEffect(() => {
    // Verify subscription status on component mount
    if (subscriptionId) {
      verifySubscriptionStatus();
    }
  }, [subscriptionId, verifySubscriptionStatus]);

  const handleCancelSubscription = async () => {
    if (!subscriptionId) return;
    
    setCancelling(true);
    try {
      const result = await cancelSubscription(subscriptionId);
      if (result.error) {
        throw new Error(result.error);
      }
      
      // Clear subscription data
      clearSubscription();
      setShowCancelConfirm(false);
      
      // Show success message or redirect
      alert('Subscription cancelled successfully. You will continue to have access until the end of your billing period.');
      
    } catch (error: any) {
      console.error('Cancellation error:', error);
      alert('Failed to cancel subscription. Please contact support.');
    } finally {
      setCancelling(false);
    }
  };

  const handleContactSupport = () => {
    window.location.href = `mailto:${getSupportEmail()}?subject=Subscription Support&body=Hi, I need help with my FastCodr Premium subscription.`;
  };

  if (!isSubscribed) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
        <div className="text-center">
          <Crown className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
            No Active Subscription
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Upgrade to FastCodr Premium to unlock all features.
          </p>
          <a
            href="/subscribe"
            className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
          >
            <Crown className="w-4 h-4" />
            Upgrade to Premium
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Subscription Status */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-full">
              <Crown className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
                FastCodr Premium
              </h3>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-green-600 dark:text-green-400 font-medium">Active</span>
              </div>
            </div>
          </div>
          
          <button
            onClick={verifySubscriptionStatus}
            disabled={isLoading}
            className="flex items-center gap-2 px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>

        {/* Subscription Details */}
        <div className="grid md:grid-cols-2 gap-4">
          <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <CreditCard className="w-5 h-5 text-blue-500" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Plan</p>
              <p className="font-semibold text-gray-800 dark:text-gray-200">Premium Monthly</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <Calendar className="w-5 h-5 text-purple-500" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Billing</p>
              <p className="font-semibold text-gray-800 dark:text-gray-200">$10.00 / month</p>
            </div>
          </div>
        </div>

        {/* Subscription Data */}
        {subscriptionData && (
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              <strong>Subscription ID:</strong> {subscriptionId}
            </p>
            {subscriptionData.next_billing_date && (
              <p className="text-sm text-gray-600 dark:text-gray-400">
                <strong>Next Billing:</strong> {new Date(subscriptionData.next_billing_date).toLocaleDateString()}
              </p>
            )}
          </div>
        )}

        {error && (
          <div className="mt-4 p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
          Manage Subscription
        </h4>
        
        <div className="space-y-3">
          <button
            onClick={handleContactSupport}
            className="w-full flex items-center gap-3 p-3 text-left border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <Mail className="w-5 h-5 text-blue-500" />
            <div>
              <p className="font-medium text-gray-800 dark:text-gray-200">Contact Support</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Get help with your subscription</p>
            </div>
          </button>

          <button
            onClick={() => setShowCancelConfirm(true)}
            className="w-full flex items-center gap-3 p-3 text-left border border-red-200 dark:border-red-600 rounded-lg hover:bg-red-50 dark:hover:bg-red-950/20 transition-colors"
          >
            <AlertTriangle className="w-5 h-5 text-red-500" />
            <div>
              <p className="font-medium text-red-600 dark:text-red-400">Cancel Subscription</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">End your premium subscription</p>
            </div>
          </button>
        </div>
      </div>

      {/* Cancel Confirmation Modal */}
      {showCancelConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center gap-3 mb-4">
              <AlertTriangle className="w-6 h-6 text-red-500" />
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                Cancel Subscription
              </h3>
            </div>
            
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Are you sure you want to cancel your FastCodr Premium subscription? 
              You'll lose access to premium features at the end of your billing period.
            </p>
            
            <div className="flex gap-3">
              <button
                onClick={handleCancelSubscription}
                disabled={cancelling}
                className="flex-1 py-2 px-4 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
              >
                {cancelling ? 'Cancelling...' : 'Yes, Cancel'}
              </button>
              <button
                onClick={() => setShowCancelConfirm(false)}
                className="flex-1 py-2 px-4 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Keep Subscription
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
