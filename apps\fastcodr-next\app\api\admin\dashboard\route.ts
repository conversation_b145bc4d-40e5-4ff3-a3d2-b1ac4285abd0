import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, UserRole, Permission } from '@/lib/auth';

interface DashboardStats {
  users: {
    total: number;
    active: number;
    new: number;
    subscribers: number;
    admins: number;
    growth: number; // percentage
  };
  projects: {
    total: number;
    public: number;
    private: number;
    templates: number;
    totalSize: number;
    averageSize: number;
    growth: number;
  };
  subscriptions: {
    active: number;
    revenue: number;
    churnRate: number;
    conversionRate: number;
    mrr: number; // Monthly Recurring Revenue
    growth: number;
  };
  system: {
    uptime: number;
    responseTime: number;
    errorRate: number;
    storageUsed: number;
    storageLimit: number;
    apiCalls: number;
  };
  activity: {
    date: string;
    users: number;
    projects: number;
    subscriptions: number;
    revenue: number;
  }[];
}

// GET /api/admin/dashboard - Get dashboard statistics
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(UserRole.ADMIN, [Permission.ADMIN_DASHBOARD])(request);
    
    if (authResult instanceof Response) {
      return authResult;
    }

    const { user } = authResult;
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '30d'; // 7d, 30d, 90d, 1y

    // Mock data - replace with actual database queries
    const mockStats: DashboardStats = {
      users: {
        total: 1247,
        active: 892,
        new: 156,
        subscribers: 234,
        admins: 3,
        growth: 12.5
      },
      projects: {
        total: 3456,
        public: 1234,
        private: 2222,
        templates: 45,
        totalSize: 1024 * 1024 * 500, // 500MB
        averageSize: 1024 * 150, // 150KB
        growth: 8.3
      },
      subscriptions: {
        active: 234,
        revenue: 2340, // $2,340
        churnRate: 3.2,
        conversionRate: 18.7,
        mrr: 2340,
        growth: 15.2
      },
      system: {
        uptime: 99.9,
        responseTime: 245, // ms
        errorRate: 0.12,
        storageUsed: 1024 * 1024 * 1024 * 2.5, // 2.5GB
        storageLimit: 1024 * 1024 * 1024 * 10, // 10GB
        apiCalls: 45678
      },
      activity: generateMockActivity(timeRange)
    };

    // Calculate additional metrics
    const metrics = {
      userEngagement: (mockStats.users.active / mockStats.users.total) * 100,
      projectsPerUser: mockStats.projects.total / mockStats.users.total,
      subscriptionRate: (mockStats.users.subscribers / mockStats.users.total) * 100,
      storageUtilization: (mockStats.system.storageUsed / mockStats.system.storageLimit) * 100,
      revenuePerUser: mockStats.subscriptions.revenue / mockStats.users.subscribers
    };

    return NextResponse.json({
      success: true,
      data: {
        stats: mockStats,
        metrics,
        timeRange,
        lastUpdated: new Date()
      }
    });

  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard statistics' },
      { status: 500 }
    );
  }
}

// Generate mock activity data based on time range
function generateMockActivity(timeRange: string) {
  const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365;
  const activity = [];
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    
    activity.push({
      date: date.toISOString().split('T')[0],
      users: Math.floor(Math.random() * 50) + 20,
      projects: Math.floor(Math.random() * 30) + 10,
      subscriptions: Math.floor(Math.random() * 5) + 1,
      revenue: Math.floor(Math.random() * 200) + 50
    });
  }
  
  return activity;
}
