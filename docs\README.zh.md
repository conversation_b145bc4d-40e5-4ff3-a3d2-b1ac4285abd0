# FastCodr - 更智能、更快速的编程 ⚡

> **FastCodr 是一个闪电般快速的编程伴侣，提升开发者生产力。**

![FastCodr Banner](./img/image-1.png)

## 🚀 什么是 FastCodr？

FastCodr 是您的智能、快速、专注的开发助手，革命性地改变您构建应用程序的方式。基于尖端AI技术，FastCodr 能在几秒钟内将想法转化为生产就绪的代码。

## ✨ FastCodr 有什么不同之处？

FastCodr 在众多AI编程工具（如 Cursor、v0、bolt.new）中脱颖而出，具有以下强大功能：

🔧 **浏览器开发环境**：内置 WebContainer 环境，可直接在浏览器中运行终端、安装包和调试。

🎨 **高保真设计转代码**：先进的 D2C 技术，从 Sketch、Figma 和图片文件实现 90% 的设计还原精度。

📁 **现有项目支持**：与仅限浏览器的工具不同，FastCodr 可以导入并处理您现有的代码库，实现无缝开发。

📱 **微信小程序集成**：与微信开发者工具直接集成，实现即时预览和调试。

🌐 **多平台支持**：支持 Windows、Mac 和 Web - 选择最适合您的环境。

🤖 **先进AI模型**：支持多个AI提供商，包括 OpenAI、Deepseek、Claude 等。

🔌 **MCP协议支持**：通过模型上下文协议扩展功能。

| 功能                   | FastCodr | v0  | bolt.new |
| ---------------------- | -------- | --- | -------- |
| 代码生成和预览         | ✅       | ✅  | ✅       |
| 设计稿转代码           | ✅       | ✅  | ✅       |
| 开源                   | ✅       | ❌  | ✅       |
| 支持微信小程序工具预览 | ✅       | ❌  | ❌       |
| 支持现有项目           | ✅       | ❌  | ❌       |
| 支持 Deepseek          | ✅       | ❌  | ❌       |
| 支持 MCP               | ✅       | ❌  | ❌       |
| 多平台桌面应用         | ✅       | ❌  | ❌       |

## 🛠️ 快速开始

### 前置要求
- Node.js 18.20 或更高版本
- pnpm 包管理器

### 1. 安装 pnpm
```bash
npm install pnpm -g
```

### 2. 安装依赖
```bash
# 安装客户端依赖
cd apps/fastcodr-client
pnpm install

# 安装服务端依赖
cd ../fastcodr-next
pnpm install
```

### 3. 配置环境变量

**客户端配置** (`apps/fastcodr-client/.env`):
```shell
# 服务端地址 [必填] (例如: http://localhost:3000)
APP_BASE_URL=

# JWT 密钥 [可选]
JWT_SECRET=
```

**服务端配置** (`apps/fastcodr-next/.env`):

```shell
# AI模型API地址 [必填] (例如: https://api.openai.com/v1)
THIRD_API_URL=
# AI模型API密钥 [必填] (例如: sk-xxxx)
THIRD_API_KEY=
# JWT密钥 [可选]
JWT_SECRET=
```

### 4. 构建和运行

**构建Web编辑器:**
```bash
chmod +x scripts/fastcodr-build.sh
./scripts/fastcodr-build.sh
```

**开发模式:**
```bash
# 启动服务端
pnpm dev:next

# 启动客户端 (在另一个终端)
pnpm dev:client
```

## 📦 安装选项

### 桌面应用程序
下载适合您平台的最新版本:
- **Windows**: 下载 `.exe` 安装程序
- **macOS**: 下载 `.dmg` 文件

### Web版本
直接在浏览器中访问FastCodr。

## 问题
- electron假如二次运行报错，请删除client workspace
- electron假如启动的时候没有preview，请运行pnpm run electron:dev

## 联系我们

发送邮件到 <a href="mailto:<EMAIL>"><EMAIL></a>

## 微信群交流群
<img src="./img/code.png" alt="alt text" width="200"/>

如果无法加入微信群，可以加

<img src="./img/self.png" alt="alt text" width="200"/>

## Star History

<a href="https://star-history.com/?utm_source=bestxtools.com#we0-dev/we0&Date">
 <picture>
   <source media="(prefers-color-scheme: dark)" srcset="https://api.star-history.com/svg?repos=we0-dev/we0&type=Date&theme=dark" />
   <source media="(prefers-color-scheme: light)" srcset="https://api.star-history.com/svg?repos=we0-dev/we0&type=Date" />
   <img alt="Star History Chart" src="https://api.star-history.com/svg?repos=we0-dev/we0&type=Date" />
 </picture>
</a>
