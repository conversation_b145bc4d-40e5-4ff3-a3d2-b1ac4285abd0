import React, { useState, useRef, useCallback } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import {
  Square,
  Type,
  Image,
  MousePointer,
  Layout,
  Grid3X3,
  Layers,
  Settings,
  Code,
  Eye,
  Smartphone,
  Tablet,
  Monitor,
  Trash2,
  Copy,
  Move
} from 'lucide-react';
import { useFileStore } from '../CodeEditor/stores/fileStore';
import { toast } from 'react-hot-toast';
import ComponentPaletteItem from './components/ComponentPaletteItem';
import ComponentTree from './components/ComponentTree';
import DropCanvas from './components/DropCanvas';
import PropertiesPanel from './components/PropertiesPanel';

// Component types for the visual builder
export interface UIComponent {
  id: string;
  type: string;
  props: Record<string, any>;
  children?: UIComponent[];
  style?: React.CSSProperties;
}

// Available component types
const COMPONENT_TYPES = {
  div: { name: 'Container', icon: Square, defaultProps: { className: 'p-4 border border-gray-300 rounded' } },
  button: { name: 'But<PERSON>', icon: MousePointer, defaultProps: { children: 'Click me', className: 'px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600' } },
  input: { name: 'Input', icon: Type, defaultProps: { type: 'text', placeholder: 'Enter text...', className: 'px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500' } },
  img: { name: 'Image', icon: Image, defaultProps: { src: 'https://via.placeholder.com/200x150', alt: 'Placeholder', className: 'max-w-full h-auto rounded' } },
  h1: { name: 'Heading 1', icon: Type, defaultProps: { children: 'Heading 1', className: 'text-3xl font-bold text-gray-900' } },
  h2: { name: 'Heading 2', icon: Type, defaultProps: { children: 'Heading 2', className: 'text-2xl font-semibold text-gray-800' } },
  p: { name: 'Paragraph', icon: Type, defaultProps: { children: 'This is a paragraph of text.', className: 'text-gray-700 leading-relaxed' } },
  span: { name: 'Text', icon: Type, defaultProps: { children: 'Text content', className: 'text-gray-600' } },
  form: { name: 'Form', icon: Layout, defaultProps: { className: 'space-y-4 p-4 border border-gray-200 rounded' } },
  section: { name: 'Section', icon: Layout, defaultProps: { className: 'py-8 px-4' } },
  nav: { name: 'Navigation', icon: Layout, defaultProps: { className: 'flex items-center justify-between p-4 bg-gray-100' } },
  header: { name: 'Header', icon: Layout, defaultProps: { className: 'bg-gray-900 text-white p-4' } },
  footer: { name: 'Footer', icon: Layout, defaultProps: { className: 'bg-gray-800 text-white p-4 text-center' } },
  ul: { name: 'List', icon: Layers, defaultProps: { className: 'list-disc list-inside space-y-1' } },
  li: { name: 'List Item', icon: Layers, defaultProps: { children: 'List item', className: 'text-gray-700' } },
};

interface VisualBuilderProps {
  onCodeGenerate?: (code: string) => void;
  className?: string;
}

const VisualBuilder: React.FC<VisualBuilderProps> = ({ onCodeGenerate, className = '' }) => {
  const [components, setComponents] = useState<UIComponent[]>([]);
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [showCode, setShowCode] = useState(false);
  const { addFile } = useFileStore();
  const canvasRef = useRef<HTMLDivElement>(null);

  // Generate unique ID for components
  const generateId = () => `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // Add component to canvas
  const addComponent = useCallback((type: string, parentId?: string) => {
    const componentType = COMPONENT_TYPES[type as keyof typeof COMPONENT_TYPES];
    if (!componentType) return;

    const newComponent: UIComponent = {
      id: generateId(),
      type,
      props: { ...componentType.defaultProps },
      children: [],
    };

    setComponents(prev => {
      if (parentId) {
        // Add as child to existing component
        const addToParent = (comps: UIComponent[]): UIComponent[] => {
          return comps.map(comp => {
            if (comp.id === parentId) {
              return {
                ...comp,
                children: [...(comp.children || []), newComponent]
              };
            }
            if (comp.children) {
              return {
                ...comp,
                children: addToParent(comp.children)
              };
            }
            return comp;
          });
        };
        return addToParent(prev);
      } else {
        // Add as root component
        return [...prev, newComponent];
      }
    });

    setSelectedComponent(newComponent.id);
    toast.success(`${componentType.name} added to canvas`);
  }, []);

  // Remove component
  const removeComponent = useCallback((id: string) => {
    const removeFromTree = (comps: UIComponent[]): UIComponent[] => {
      return comps.filter(comp => comp.id !== id).map(comp => ({
        ...comp,
        children: comp.children ? removeFromTree(comp.children) : []
      }));
    };

    setComponents(prev => removeFromTree(prev));
    setSelectedComponent(null);
    toast.success('Component removed');
  }, []);

  // Update component props
  const updateComponent = useCallback((id: string, updates: Partial<UIComponent>) => {
    const updateInTree = (comps: UIComponent[]): UIComponent[] => {
      return comps.map(comp => {
        if (comp.id === id) {
          return { ...comp, ...updates };
        }
        if (comp.children) {
          return {
            ...comp,
            children: updateInTree(comp.children)
          };
        }
        return comp;
      });
    };

    setComponents(prev => updateInTree(prev));
  }, []);

  // Generate React code from components
  const generateCode = useCallback(() => {
    const generateComponentCode = (comp: UIComponent, indent = 0): string => {
      const indentStr = '  '.repeat(indent);
      const { type, props, children } = comp;

      // Handle props
      const propsStr = Object.entries(props)
        .filter(([key, value]) => key !== 'children' && value !== undefined)
        .map(([key, value]) => {
          if (typeof value === 'string') {
            return `${key}="${value}"`;
          }
          return `${key}={${JSON.stringify(value)}}`;
        })
        .join(' ');

      // Handle children
      const hasTextContent = props.children && typeof props.children === 'string';
      const hasChildComponents = children && children.length > 0;

      if (!hasTextContent && !hasChildComponents) {
        return `${indentStr}<${type}${propsStr ? ' ' + propsStr : ''} />`;
      }

      let result = `${indentStr}<${type}${propsStr ? ' ' + propsStr : ''}>`;

      if (hasTextContent) {
        result += props.children;
      }

      if (hasChildComponents) {
        result += '\n';
        result += children!.map(child => generateComponentCode(child, indent + 1)).join('\n');
        result += '\n' + indentStr;
      }

      result += `</${type}>`;
      return result;
    };

    const componentCode = components.map(comp => generateComponentCode(comp)).join('\n\n');

    const fullCode = `import React from 'react';

const GeneratedComponent = () => {
  return (
    <div className="min-h-screen bg-gray-50 p-4">
${componentCode.split('\n').map(line => '      ' + line).join('\n')}
    </div>
  );
};

export default GeneratedComponent;`;

    return fullCode;
  }, [components]);

  // Export code to file
  const exportCode = useCallback(() => {
    const code = generateCode();
    const fileName = `generated-component-${Date.now()}.tsx`;
    addFile(fileName, code);
    onCodeGenerate?.(code);
    toast.success(`Code exported to ${fileName}`);
  }, [generateCode, addFile, onCodeGenerate]);

  // Get viewport classes for responsive preview
  const getViewportClasses = () => {
    switch (viewMode) {
      case 'mobile':
        return 'w-80 h-[600px]';
      case 'tablet':
        return 'w-[768px] h-[1024px]';
      case 'desktop':
      default:
        return 'w-full h-full';
    }
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className={`flex h-full bg-gray-100 ${className}`}>
        {/* Component Palette */}
        <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <h3 className="font-semibold text-gray-900 mb-2">Components</h3>
            <div className="grid grid-cols-2 gap-2">
              {Object.entries(COMPONENT_TYPES).map(([type, config]) => (
                <ComponentPaletteItem
                  key={type}
                  type={type}
                  config={config}
                  onAdd={() => addComponent(type)}
                />
              ))}
            </div>
          </div>

          {/* Component Tree */}
          <div className="flex-1 p-4 overflow-y-auto">
            <h4 className="font-medium text-gray-700 mb-2">Component Tree</h4>
            <ComponentTree
              components={components}
              selectedId={selectedComponent}
              onSelect={setSelectedComponent}
              onRemove={removeComponent}
            />
          </div>
        </div>

        {/* Canvas Area */}
        <div className="flex-1 flex flex-col">
          {/* Toolbar */}
          <div className="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h2 className="font-semibold text-gray-900">Visual Builder</h2>

              {/* Viewport Controls */}
              <div className="flex items-center gap-2 bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('desktop')}
                  className={`p-2 rounded ${viewMode === 'desktop' ? 'bg-white shadow' : 'hover:bg-gray-200'}`}
                  title="Desktop View"
                >
                  <Monitor className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('tablet')}
                  className={`p-2 rounded ${viewMode === 'tablet' ? 'bg-white shadow' : 'hover:bg-gray-200'}`}
                  title="Tablet View"
                >
                  <Tablet className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('mobile')}
                  className={`p-2 rounded ${viewMode === 'mobile' ? 'bg-white shadow' : 'hover:bg-gray-200'}`}
                  title="Mobile View"
                >
                  <Smartphone className="w-4 h-4" />
                </button>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowCode(!showCode)}
                className="flex items-center gap-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                {showCode ? <Eye className="w-4 h-4" /> : <Code className="w-4 h-4" />}
                {showCode ? 'Preview' : 'Code'}
              </button>

              <button
                onClick={exportCode}
                className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
              >
                <Code className="w-4 h-4" />
                Export Code
              </button>
            </div>
          </div>

          {/* Canvas */}
          <div className="flex-1 p-4 overflow-auto bg-gray-50">
            {showCode ? (
              <div className="bg-white rounded-lg border border-gray-200 h-full">
                <div className="p-4 border-b border-gray-200">
                  <h3 className="font-medium text-gray-900">Generated Code</h3>
                </div>
                <pre className="p-4 text-sm overflow-auto h-full">
                  <code>{generateCode()}</code>
                </pre>
              </div>
            ) : (
              <div className="flex justify-center items-start">
                <div className={`bg-white rounded-lg shadow-lg border border-gray-200 transition-all duration-300 ${getViewportClasses()}`}>
                  <DropCanvas
                    ref={canvasRef}
                    components={components}
                    selectedId={selectedComponent}
                    onSelect={setSelectedComponent}
                    onDrop={addComponent}
                    onUpdate={updateComponent}
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Properties Panel */}
        {selectedComponent && (
          <PropertiesPanel
            component={components.find(c => findComponentById(c, selectedComponent))}
            onUpdate={(updates) => updateComponent(selectedComponent, updates)}
            onRemove={() => removeComponent(selectedComponent)}
          />
        )}
      </div>
    </DndProvider>
  );
};

// Helper function to find component by ID in tree
const findComponentById = (comp: UIComponent, id: string): UIComponent | null => {
  if (comp.id === id) return comp;
  if (comp.children) {
    for (const child of comp.children) {
      const found = findComponentById(child, id);
      if (found) return found;
    }
  }
  return null;
};

export default VisualBuilder;
