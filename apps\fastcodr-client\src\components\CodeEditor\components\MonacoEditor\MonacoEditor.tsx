import React, { useRef, useEffect, useState } from 'react';
import * as monaco from 'monaco-editor';
import { useFileStore } from '../../stores/fileStore';
import { useEditorStore } from '../../stores/editorStore';
import useThemeStore from '@/stores/themeSlice';
import { useAIAssistant } from '@/hooks/useAIAssistant';
import CodeContextMenu from '@/components/AIAssistant/CodeContextMenu';
import AISuggestions from '@/components/AIAssistant/AISuggestions';
import { toast } from 'react-hot-toast';

// Import Monaco Editor workers
import editorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker';
import jsonWorker from 'monaco-editor/esm/vs/language/json/json.worker?worker';
import cssWorker from 'monaco-editor/esm/vs/language/css/css.worker?worker';
import htmlWorker from 'monaco-editor/esm/vs/language/html/html.worker?worker';
import tsWorker from 'monaco-editor/esm/vs/language/typescript/ts.worker?worker';

// Configure Monaco workers
self.MonacoEnvironment = {
  getWorker(_, label) {
    if (label === 'json') {
      return new jsonWorker();
    }
    if (label === 'css' || label === 'scss' || label === 'less') {
      return new cssWorker();
    }
    if (label === 'html' || label === 'handlebars' || label === 'razor') {
      return new htmlWorker();
    }
    if (label === 'typescript' || label === 'javascript') {
      return new tsWorker();
    }
    return new editorWorker();
  },
};

interface MonacoEditorProps {
  fileName: string;
  initialLine?: number;
  className?: string;
}

const MonacoEditor: React.FC<MonacoEditorProps> = ({ 
  fileName, 
  initialLine, 
  className = '' 
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const monacoRef = useRef<monaco.editor.IStandaloneCodeEditor | null>(null);
  const { getContent, updateContent } = useFileStore();
  const { setDirty, setCurrentFile } = useEditorStore();
  const { isDarkMode } = useThemeStore();
  const [contextMenu, setContextMenu] = useState({
    visible: false,
    position: { x: 0, y: 0 },
    selectedCode: ''
  });
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedCodeForSuggestions, setSelectedCodeForSuggestions] = useState('');

  const { canUseAI } = useAIAssistant();

  // Get language from file extension
  const getLanguage = (fileName: string): string => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'ts': return 'typescript';
      case 'tsx': return 'typescript';
      case 'js': return 'javascript';
      case 'jsx': return 'javascript';
      case 'json': return 'json';
      case 'css': return 'css';
      case 'scss': return 'scss';
      case 'less': return 'less';
      case 'html': return 'html';
      case 'xml': return 'xml';
      case 'md': return 'markdown';
      case 'py': return 'python';
      case 'java': return 'java';
      case 'cpp': case 'cc': case 'cxx': return 'cpp';
      case 'c': return 'c';
      case 'cs': return 'csharp';
      case 'php': return 'php';
      case 'rb': return 'ruby';
      case 'go': return 'go';
      case 'rs': return 'rust';
      case 'sql': return 'sql';
      case 'yaml': case 'yml': return 'yaml';
      default: return 'plaintext';
    }
  };

  // Configure FastCodr theme
  const configureFastCodrTheme = () => {
    monaco.editor.defineTheme('fastcodr-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6A9955', fontStyle: 'italic' },
        { token: 'keyword', foreground: '569CD6' },
        { token: 'operator', foreground: 'D4D4D4' },
        { token: 'namespace', foreground: '4EC9B0' },
        { token: 'type', foreground: '4EC9B0' },
        { token: 'struct', foreground: '4EC9B0' },
        { token: 'class', foreground: '4EC9B0' },
        { token: 'interface', foreground: '4EC9B0' },
        { token: 'parameter', foreground: '9CDCFE' },
        { token: 'variable', foreground: '9CDCFE' },
        { token: 'property', foreground: '9CDCFE' },
        { token: 'enumMember', foreground: '4FC1FF' },
        { token: 'function', foreground: 'DCDCAA' },
        { token: 'member', foreground: 'DCDCAA' },
        { token: 'macro', foreground: '569CD6' },
        { token: 'string', foreground: 'CE9178' },
        { token: 'number', foreground: 'B5CEA8' },
        { token: 'regexp', foreground: 'D16969' },
        { token: 'delimiter', foreground: 'D4D4D4' },
      ],
      colors: {
        'editor.background': '#1E90FF10', // FastCodr blue tint
        'editor.foreground': '#D4D4D4',
        'editorLineNumber.foreground': '#858585',
        'editorLineNumber.activeForeground': '#C6C6C6',
        'editor.selectionBackground': '#264F78',
        'editor.selectionHighlightBackground': '#ADD6FF26',
        'editor.wordHighlightBackground': '#575757B8',
        'editor.wordHighlightStrongBackground': '#004972B8',
        'editorCursor.foreground': '#AEAFAD',
        'editor.lineHighlightBackground': '#282C3410',
        'editorWhitespace.foreground': '#404040',
        'editorIndentGuide.background': '#404040',
        'editorIndentGuide.activeBackground': '#707070',
      }
    });

    monaco.editor.defineTheme('fastcodr-light', {
      base: 'vs',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '008000', fontStyle: 'italic' },
        { token: 'keyword', foreground: '0000FF' },
        { token: 'string', foreground: 'A31515' },
        { token: 'number', foreground: '098658' },
        { token: 'function', foreground: '795E26' },
        { token: 'type', foreground: '267F99' },
        { token: 'class', foreground: '267F99' },
        { token: 'interface', foreground: '267F99' },
      ],
      colors: {
        'editor.background': '#1E90FF05', // Light FastCodr blue tint
        'editor.foreground': '#000000',
        'editorLineNumber.foreground': '#237893',
        'editor.selectionBackground': '#ADD6FF',
        'editor.lineHighlightBackground': '#F6F6F6',
      }
    });
  };

  // Initialize Monaco Editor
  useEffect(() => {
    if (!editorRef.current) return;

    configureFastCodrTheme();

    const editor = monaco.editor.create(editorRef.current, {
      value: getContent(fileName),
      language: getLanguage(fileName),
      theme: isDarkMode ? 'fastcodr-dark' : 'fastcodr-light',
      fontSize: 14,
      fontFamily: '"Fira Code", "Cascadia Code", "JetBrains Mono", monospace',
      fontLigatures: true,
      lineNumbers: 'on',
      roundedSelection: false,
      scrollBeyondLastLine: false,
      automaticLayout: true,
      minimap: { enabled: true },
      wordWrap: 'on',
      tabSize: 2,
      insertSpaces: true,
      detectIndentation: true,
      folding: true,
      foldingStrategy: 'indentation',
      showFoldingControls: 'always',
      unfoldOnClickAfterEndOfLine: false,
      contextmenu: true,
      quickSuggestions: {
        other: true,
        comments: false,
        strings: false
      },
      suggestOnTriggerCharacters: true,
      acceptSuggestionOnCommitCharacter: true,
      acceptSuggestionOnEnter: 'on',
      accessibilitySupport: 'auto',
      inlayHints: { enabled: 'on' },
      bracketPairColorization: { enabled: true },
      guides: {
        bracketPairs: true,
        indentation: true
      },
      renderWhitespace: 'selection',
      renderControlCharacters: false,
      renderLineHighlight: 'line',
      cursorBlinking: 'blink',
      cursorSmoothCaretAnimation: 'on',
      smoothScrolling: true,
      mouseWheelZoom: true,
    });

    monacoRef.current = editor;

    // Handle content changes
    const disposable = editor.onDidChangeModelContent(() => {
      const content = editor.getValue();
      updateContent(fileName, content);
      setDirty(fileName, true);
      setCurrentFile(fileName);
    });

    // Handle right-click context menu for AI assistance
    const contextMenuDisposable = editor.onContextMenu((e) => {
      if (!canUseAI) return;
      
      const selection = editor.getSelection();
      if (selection && !selection.isEmpty()) {
        const selectedText = editor.getModel()?.getValueInRange(selection) || '';
        if (selectedText.trim()) {
          setContextMenu({
            visible: true,
            position: { x: e.event.posx, y: e.event.posy },
            selectedCode: selectedText
          });
        }
      }
    });

    // Handle selection changes for AI suggestions
    const selectionDisposable = editor.onDidChangeCursorSelection((e) => {
      const selection = editor.getSelection();
      if (selection && !selection.isEmpty()) {
        const selectedText = editor.getModel()?.getValueInRange(selection) || '';
        if (selectedText.trim() && selectedText.length > 10) {
          setSelectedCodeForSuggestions(selectedText);
          setShowSuggestions(true);
        }
      } else {
        setShowSuggestions(false);
      }
    });

    // Go to specific line if provided
    if (initialLine) {
      editor.revealLineInCenter(initialLine);
      editor.setPosition({ lineNumber: initialLine, column: 1 });
    }

    return () => {
      disposable.dispose();
      contextMenuDisposable.dispose();
      selectionDisposable.dispose();
      editor.dispose();
    };
  }, [fileName]);

  // Update theme when dark mode changes
  useEffect(() => {
    if (monacoRef.current) {
      monaco.editor.setTheme(isDarkMode ? 'fastcodr-dark' : 'fastcodr-light');
    }
  }, [isDarkMode]);

  // Update content when file content changes externally
  useEffect(() => {
    if (monacoRef.current) {
      const currentContent = monacoRef.current.getValue();
      const newContent = getContent(fileName);
      if (currentContent !== newContent) {
        monacoRef.current.setValue(newContent);
      }
    }
  }, [getContent(fileName)]);

  const handleInsertCode = (code: string) => {
    if (monacoRef.current) {
      const selection = monacoRef.current.getSelection();
      if (selection) {
        monacoRef.current.executeEdits('ai-assistant', [{
          range: selection,
          text: code
        }]);
        toast.success('Code inserted successfully!');
      }
    }
    setContextMenu(prev => ({ ...prev, visible: false }));
  };

  const handleCloseContextMenu = () => {
    setContextMenu(prev => ({ ...prev, visible: false }));
  };

  return (
    <div className={`relative h-full w-full ${className}`}>
      <div ref={editorRef} className="h-full w-full" />
      
      {/* AI Context Menu */}
      <CodeContextMenu
        isVisible={contextMenu.visible}
        position={contextMenu.position}
        selectedCode={contextMenu.selectedCode}
        onClose={handleCloseContextMenu}
        onInsertCode={handleInsertCode}
      />
      
      {/* AI Suggestions Panel */}
      {showSuggestions && canUseAI && (
        <div className="absolute top-4 right-4 w-80 z-10">
          <AISuggestions
            selectedCode={selectedCodeForSuggestions}
            onInsertCode={handleInsertCode}
            onClose={() => setShowSuggestions(false)}
          />
        </div>
      )}
    </div>
  );
};

export default MonacoEditor;
