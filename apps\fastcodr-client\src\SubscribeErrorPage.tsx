import React, { useEffect } from 'react';
import { setSubscriptionStatus, getSupportEmail } from './lib/cashfree';
import useSubscriptionStore from './stores/subscriptionSlice';
import { XCircle, RefreshCw, Mail, ArrowLeft } from 'lucide-react';

export default function SubscribeErrorPage() {
  const { clearSubscription } = useSubscriptionStore();

  useEffect(() => {
    // Clear any pending subscription data
    setSubscriptionStatus(false);
    clearSubscription();
    localStorage.removeItem('pending_subscription_id');
  }, [clearSubscription]);

  const handleRetry = () => {
    window.location.href = '/subscribe';
  };

  const handleGoBack = () => {
    window.location.href = '/';
  };

  const handleContactSupport = () => {
    window.location.href = `mailto:${getSupportEmail()}?subject=Subscription Payment Issue&body=Hi, I encountered an issue with my FastCodr Premium subscription payment. Please help me resolve this.`;
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 dark:from-red-950 dark:via-gray-900 dark:to-orange-950">
      <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-8 max-w-lg w-full text-center">
        {/* Error Icon */}
        <div className="flex items-center justify-center mb-6">
          <div className="w-20 h-20 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center">
            <XCircle className="w-12 h-12 text-white" />
          </div>
        </div>

        {/* Error Message */}
        <h1 className="text-3xl font-bold mb-4 text-red-600 dark:text-red-400">
          Payment Failed
        </h1>

        <p className="text-lg text-gray-600 dark:text-gray-400 mb-6">
          We couldn't process your payment for FastCodr Premium. This could be due to:
        </p>

        {/* Common Issues */}
        <div className="bg-red-50 dark:bg-red-950/20 rounded-lg p-4 mb-6 text-left">
          <ul className="space-y-2 text-sm text-gray-700 dark:text-gray-300">
            <li>• Insufficient funds in your account</li>
            <li>• Card details entered incorrectly</li>
            <li>• Bank security restrictions</li>
            <li>• Network connectivity issues</li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={handleRetry}
            className="w-full py-3 px-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg shadow-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105"
          >
            <div className="flex items-center justify-center gap-2">
              <RefreshCw className="w-5 h-5" />
              Try Again
            </div>
          </button>

          <button
            onClick={handleContactSupport}
            className="w-full py-3 px-6 border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-semibold rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            <div className="flex items-center justify-center gap-2">
              <Mail className="w-5 h-5" />
              Contact Support
            </div>
          </button>

          <button
            onClick={handleGoBack}
            className="w-full py-3 px-6 text-gray-500 dark:text-gray-400 font-medium hover:text-gray-700 dark:hover:text-gray-200 transition-colors"
          >
            <div className="flex items-center justify-center gap-2">
              <ArrowLeft className="w-4 h-4" />
              Back to Dashboard
            </div>
          </button>
        </div>

        <p className="text-xs text-gray-500 dark:text-gray-400 mt-6">
          No charges were made to your account. You can safely retry the payment.
        </p>
      </div>
    </div>
  );
}
