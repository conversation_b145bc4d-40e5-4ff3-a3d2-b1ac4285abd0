# 🚀 FastCodr Production Deployment Guide

This comprehensive guide covers deploying FastCodr to production with all necessary configurations, monitoring, and maintenance procedures.

## 📋 Quick Start Checklist

### ✅ Pre-Deployment Requirements
- [ ] Domain name configured with DNS
- [ ] SSL certificates obtained
- [ ] Server with Docker and Docker Compose installed
- [ ] MongoDB database setup (Atlas or self-hosted)
- [ ] Cashfree production account configured
- [ ] OpenAI API key for production
- [ ] Environment variables configured

### ✅ Deployment Files Created
- [ ] `docker-compose.prod.yml` - Production Docker configuration
- [ ] `docker-compose.staging.yml` - Staging environment
- [ ] `nginx/nginx.prod.conf` - Production Nginx configuration
- [ ] `scripts/backup.sh` - Automated backup script
- [ ] `scripts/restore.sh` - Disaster recovery script
- [ ] `scripts/health-check.sh` - Health monitoring script
- [ ] `.github/workflows/deploy.yml` - CI/CD pipeline

## 🔧 Environment Setup

### 1. Server Requirements

**Minimum Production Server:**
- **CPU:** 4 cores
- **RAM:** 8GB
- **Storage:** 100GB SSD
- **OS:** Ubuntu 20.04+ or CentOS 8+
- **Network:** Static IP with ports 80, 443, 22 open

**Recommended Production Server:**
- **CPU:** 8 cores
- **RAM:** 16GB
- **Storage:** 200GB SSD
- **OS:** Ubuntu 22.04 LTS
- **Network:** Load balancer with multiple instances

### 2. Domain and SSL Configuration

```bash
# Configure DNS records
yourdomain.com        A     YOUR_SERVER_IP
www.yourdomain.com    A     YOUR_SERVER_IP
api.yourdomain.com    A     YOUR_SERVER_IP

# Obtain SSL certificates with Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com -d api.yourdomain.com

# Copy certificates to nginx/ssl directory
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem nginx/ssl/
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem nginx/ssl/
sudo chown -R $USER:$USER nginx/ssl/
```

### 3. Environment Variables Configuration

**Backend Production (.env.production):**
```env
# AI Model API Configuration
THIRD_API_URL=https://api.openai.com/v1
THIRD_API_KEY=sk-your-production-openai-key

# MongoDB Connection (Production)
MONGODB_URI=mongodb+srv://username:<EMAIL>/fastcodr

# JWT Secret (Generate strong secret)
JWT_SECRET=your-super-secure-jwt-secret-256-bits

# CORS Configuration
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Security
SECURE_COOKIES=true
HTTPS_ONLY=true
NODE_ENV=production
```

**Frontend Production (.env.production):**
```env
# Backend API URL
APP_BASE_URL=https://api.yourdomain.com

# Cashfree Production
VITE_CASHFREE_CLIENT_ID=your-production-client-id
VITE_CASHFREE_CLIENT_SECRET=your-production-client-secret
VITE_CASHFREE_BASE_URL=https://api.cashfree.com/pg
VITE_CASHFREE_ENV=production

# Analytics
VITE_ANALYTICS_ID=G-XXXXXXXXXX
VITE_SENTRY_DSN=https://your-sentry-dsn
```

## 🚀 Deployment Methods

### Method 1: Automated Deployment (Recommended)

```bash
# Clone repository
git clone https://github.com/yourusername/fastcodr.git
cd fastcodr

# Configure environment files
cp apps/fastcodr-next/.env.example apps/fastcodr-next/.env.production
cp apps/fastcodr-client/.env.example apps/fastcodr-client/.env.production

# Edit environment files with production values
nano apps/fastcodr-next/.env.production
nano apps/fastcodr-client/.env.production

# Make scripts executable
chmod +x deploy.sh scripts/*.sh

# Deploy to production
./deploy.sh
```

### Method 2: Manual Docker Deployment

```bash
# Build and start production services
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d

# Check service status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f
```

### Method 3: CI/CD Pipeline (GitHub Actions)

1. **Configure GitHub Secrets:**
```bash
# Production server secrets
PRODUCTION_HOST=your-server-ip
PRODUCTION_USER=your-ssh-user
PRODUCTION_SSH_KEY=your-private-ssh-key

# Staging server secrets
STAGING_HOST=your-staging-ip
STAGING_USER=your-ssh-user
STAGING_SSH_KEY=your-staging-ssh-key

# Notification
SLACK_WEBHOOK_URL=your-slack-webhook
```

2. **Push to production branch:**
```bash
git checkout -b production
git push origin production
```

## 📊 Monitoring and Maintenance

### 1. Health Monitoring

```bash
# Run health check
./scripts/health-check.sh

# Automated health monitoring (cron job)
# Add to crontab: crontab -e
*/5 * * * * /opt/fastcodr/scripts/health-check.sh >> /var/log/fastcodr-health.log 2>&1
```

### 2. Backup Strategy

```bash
# Manual backup
./scripts/backup.sh

# Automated daily backups (cron job)
# Add to crontab: crontab -e
0 2 * * * /opt/fastcodr/scripts/backup.sh >> /var/log/fastcodr-backup.log 2>&1

# Weekly backup cleanup
0 3 * * 0 find /backups -name "*.tar.gz" -mtime +30 -delete
```

### 3. Log Management

```bash
# View application logs
docker-compose -f docker-compose.prod.yml logs -f backend
docker-compose -f docker-compose.prod.yml logs -f frontend

# Log rotation configuration
sudo nano /etc/logrotate.d/fastcodr
```

### 4. Performance Monitoring

**Access monitoring dashboards:**
- **Prometheus:** `http://your-server:9090`
- **Grafana:** `http://your-server:3000`
- **Application:** `https://yourdomain.com`

## 🔄 Updates and Maintenance

### 1. Application Updates

```bash
# Pull latest changes
git pull origin production

# Rebuild and restart services
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d

# Verify deployment
./scripts/health-check.sh
```

### 2. Database Maintenance

```bash
# MongoDB maintenance
docker-compose -f docker-compose.prod.yml exec mongodb mongosh
# Run: db.runCommand({compact: "collection_name"})

# Database backup before maintenance
./scripts/backup.sh
```

### 3. SSL Certificate Renewal

```bash
# Renew certificates
sudo certbot renew --nginx

# Copy new certificates
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem nginx/ssl/
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem nginx/ssl/

# Restart nginx
docker-compose -f docker-compose.prod.yml restart nginx
```

## 🚨 Disaster Recovery

### 1. Complete System Restore

```bash
# List available backups
./scripts/restore.sh --list

# Restore from specific backup
./scripts/restore.sh --date 20231201_143000

# Restore from latest backup
./scripts/restore.sh
```

### 2. Rollback Deployment

```bash
# Rollback to previous version
git checkout HEAD~1
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d
```

## 🔒 Security Hardening

### 1. Server Security

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Configure firewall
sudo ufw enable
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Disable root login
sudo nano /etc/ssh/sshd_config
# Set: PermitRootLogin no
sudo systemctl restart ssh
```

### 2. Application Security

```bash
# Regular security updates
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d

# Security scanning
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image fastcodr-backend:latest
```

## 📈 Scaling and Performance

### 1. Horizontal Scaling

```bash
# Scale backend instances
docker-compose -f docker-compose.prod.yml up -d --scale backend=3

# Load balancer configuration
# Update nginx/nginx.prod.conf upstream section
```

### 2. Database Scaling

```bash
# MongoDB replica set
# Configure in MONGODB_URI with multiple hosts
MONGODB_URI=*******************************************************************************
```

## 🎯 Production Checklist

### ✅ Pre-Launch
- [ ] All environment variables configured
- [ ] SSL certificates installed and valid
- [ ] Database backups configured
- [ ] Monitoring dashboards accessible
- [ ] Health checks passing
- [ ] Performance tests completed
- [ ] Security scan passed

### ✅ Post-Launch
- [ ] Monitor application performance
- [ ] Check error rates and logs
- [ ] Verify payment processing
- [ ] Test subscription functionality
- [ ] Monitor resource usage
- [ ] Set up alerting

### ✅ Ongoing Maintenance
- [ ] Daily health checks
- [ ] Weekly backups verification
- [ ] Monthly security updates
- [ ] Quarterly performance reviews
- [ ] SSL certificate renewal (every 3 months)

## 📞 Support and Troubleshooting

### Common Issues

1. **Service won't start:**
```bash
# Check logs
docker-compose -f docker-compose.prod.yml logs service-name

# Check resource usage
docker stats

# Restart service
docker-compose -f docker-compose.prod.yml restart service-name
```

2. **Database connection issues:**
```bash
# Test MongoDB connection
docker-compose -f docker-compose.prod.yml exec backend npm run test:db

# Check MongoDB logs
docker-compose -f docker-compose.prod.yml logs mongodb
```

3. **SSL certificate issues:**
```bash
# Check certificate validity
openssl x509 -in nginx/ssl/fullchain.pem -text -noout

# Test SSL configuration
curl -I https://yourdomain.com
```

### Emergency Contacts
- **System Administrator:** <EMAIL>
- **Database Administrator:** <EMAIL>
- **DevOps Team:** <EMAIL>

---

## 🎉 Deployment Complete!

Your FastCodr application is now running in production with:
- ✅ High availability configuration
- ✅ Automated backups and monitoring
- ✅ SSL security and performance optimization
- ✅ CI/CD pipeline for updates
- ✅ Comprehensive logging and alerting

**Access your application:**
- **Frontend:** https://yourdomain.com
- **Backend API:** https://api.yourdomain.com
- **Monitoring:** https://yourdomain.com:3000 (Grafana)

For support and updates, refer to the troubleshooting section or contact the development team.
