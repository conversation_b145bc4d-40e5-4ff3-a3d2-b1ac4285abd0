# FastCodr Backend Staging Environment Configuration

# Environment
NODE_ENV=staging

# AI Model API Configuration
THIRD_API_URL=https://api.openai.com/v1
THIRD_API_KEY=your_staging_openai_api_key

# JWT Secret (Staging)
JWT_SECRET=your_staging_jwt_secret_here

# MongoDB Connection (Staging)
MONGODB_URI=**************************************************************************

# ScreenshotOne API Key for landing page screenshots
SCREENSHOTONE_API_KEY=your_screenshotone_api_key

# CORS Configuration
ALLOWED_ORIGINS=http://staging.yourdomain.com,http://localhost:8080

# Rate Limiting (More relaxed for staging)
RATE_LIMIT_REQUESTS_PER_MINUTE=200
RATE_LIMIT_REQUESTS_PER_HOUR=2000

# Logging
LOG_LEVEL=debug
LOG_FILE_PATH=/app/logs/staging.log

# Security (Less strict for staging)
SECURE_COOKIES=false
HTTPS_ONLY=false

# Performance
ENABLE_COMPRESSION=true
CACHE_TTL=1800

# Monitoring
SENTRY_DSN=your_staging_sentry_dsn
ENABLE_METRICS=true

# Debug Features (Enabled for staging)
ENABLE_DEBUG_ROUTES=true
ENABLE_VERBOSE_LOGGING=true
ENABLE_REQUEST_LOGGING=true

# Database Settings
DB_POOL_SIZE=5
DB_TIMEOUT=10000

# Redis Configuration
REDIS_URL=redis://staging_redis_password@redis:6379
REDIS_TTL=3600

# Email Configuration (Staging)
SMTP_HOST=smtp.mailtrap.io
SMTP_PORT=2525
SMTP_USER=your_mailtrap_user
SMTP_PASS=your_mailtrap_pass
FROM_EMAIL=<EMAIL>

# Feature Flags (Staging)
ENABLE_NEW_FEATURES=true
ENABLE_EXPERIMENTAL_FEATURES=true
ENABLE_BETA_FEATURES=true
