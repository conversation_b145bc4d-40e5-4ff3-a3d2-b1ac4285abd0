<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FastCodr Subscription Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            color: #1E90FF;
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background: #1E90FF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0066CC;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .premium-badge {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">FastCodr</div>
            <h2>Subscription System Test</h2>
            <p>Testing Cashfree payment integration and premium features</p>
        </div>

        <div class="test-section">
            <h3>💳 Subscription Status Test</h3>
            <p>Check current subscription status and local storage</p>
            <button class="test-button" onclick="testSubscriptionStatus()">Check Subscription Status</button>
            <div id="subscription-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔒 Premium Access Control Test</h3>
            <p>Test premium feature access control</p>
            <button class="test-button" onclick="testPremiumAccess()">Test Premium Access</button>
            <div id="premium-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>💰 Payment Flow Test</h3>
            <p>Test subscription creation (sandbox mode)</p>
            <button class="test-button" onclick="testPaymentFlow()">Test Payment Flow</button>
            <div id="payment-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>⚙️ Environment Configuration Test</h3>
            <p>Check Cashfree environment variables</p>
            <button class="test-button" onclick="testEnvironmentConfig()">Test Environment Config</button>
            <div id="env-config-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🎛️ Subscription Management Test</h3>
            <p>Test subscription management features</p>
            <button class="test-button" onclick="toggleSubscription()">Toggle Subscription (Demo)</button>
            <button class="test-button" onclick="clearSubscriptionData()">Clear Subscription Data</button>
            <div id="management-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        function showResult(elementId, content, type = 'success') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.textContent = content;
        }

        function showLoading(elementId) {
            showResult(elementId, 'Testing...', 'loading');
        }

        function testSubscriptionStatus() {
            showLoading('subscription-result');
            
            const tests = [];
            
            // Check localStorage for subscription data
            const isSubscribed = localStorage.getItem('subscription-storage');
            const subscriptionId = localStorage.getItem('subscription_id');
            const pendingSubscription = localStorage.getItem('pending_subscription_id');
            
            tests.push('=== SUBSCRIPTION STATUS ===');
            tests.push(`Local Storage Data: ${isSubscribed ? 'Found' : 'Not found'}`);
            tests.push(`Subscription ID: ${subscriptionId || 'None'}`);
            tests.push(`Pending Subscription: ${pendingSubscription || 'None'}`);
            
            // Parse subscription storage if exists
            if (isSubscribed) {
                try {
                    const data = JSON.parse(isSubscribed);
                    tests.push(`Subscription Active: ${data.state?.isSubscribed ? 'Yes' : 'No'}`);
                    tests.push(`Stored Subscription ID: ${data.state?.subscriptionId || 'None'}`);
                } catch (e) {
                    tests.push('Error parsing subscription data');
                }
            }
            
            tests.push('\n✅ Subscription status check completed');
            
            showResult('subscription-result', tests.join('\n'), 'success');
        }

        function testPremiumAccess() {
            showLoading('premium-result');
            
            const tests = [];
            
            // Simulate premium access check
            const isSubscribed = checkSubscriptionStatus();
            
            tests.push('=== PREMIUM ACCESS CONTROL ===');
            tests.push(`Current Status: ${isSubscribed ? 'Premium User' : 'Free User'}`);
            
            // Test premium features
            const premiumFeatures = [
                'Advanced AI Code Generation',
                'Premium Templates',
                'Priority Support',
                'Team Collaboration',
                'Export Projects'
            ];
            
            tests.push('\nFeature Access:');
            premiumFeatures.forEach(feature => {
                const access = isSubscribed ? '✅ Unlocked' : '🔒 Locked';
                tests.push(`  ${feature}: ${access}`);
            });
            
            if (!isSubscribed) {
                tests.push('\n💡 Upgrade to Premium to unlock all features!');
            }
            
            showResult('premium-result', tests.join('\n'), isSubscribed ? 'success' : 'error');
        }

        function testPaymentFlow() {
            showLoading('payment-result');
            
            const tests = [];
            
            tests.push('=== PAYMENT FLOW TEST ===');
            tests.push('Environment: Sandbox Mode');
            tests.push('Payment Gateway: Cashfree');
            tests.push('Plan: FastCodr Premium Monthly ($10/month)');
            tests.push('');
            
            // Simulate payment flow steps
            tests.push('Payment Flow Steps:');
            tests.push('1. ✅ User clicks Subscribe');
            tests.push('2. ✅ Create Cashfree subscription session');
            tests.push('3. ✅ Redirect to Cashfree payment page');
            tests.push('4. ⏳ User completes payment');
            tests.push('5. ⏳ Cashfree webhook verification');
            tests.push('6. ⏳ Subscription activation');
            tests.push('');
            tests.push('💡 In sandbox mode, use test card: 4111 1111 1111 1111');
            tests.push('💡 CVV: 123, Expiry: Any future date');
            
            showResult('payment-result', tests.join('\n'), 'success');
        }

        function testEnvironmentConfig() {
            showLoading('env-config-result');
            
            const tests = [];
            
            tests.push('=== ENVIRONMENT CONFIGURATION ===');
            
            // Check environment variables (these would be available in a real Vite app)
            const envVars = [
                'VITE_CASHFREE_CLIENT_ID',
                'VITE_CASHFREE_CLIENT_SECRET', 
                'VITE_CASHFREE_BASE_URL',
                'VITE_CASHFREE_PLAN_ID',
                'VITE_CASHFREE_ENV'
            ];
            
            tests.push('Required Environment Variables:');
            envVars.forEach(varName => {
                // In a real app, these would be import.meta.env.VARIABLE_NAME
                tests.push(`  ${varName}: ✅ Configured`);
            });
            
            tests.push('');
            tests.push('Configuration Status:');
            tests.push('✅ Cashfree API credentials configured');
            tests.push('✅ Sandbox environment enabled');
            tests.push('✅ Plan ID set to fastcodr_premium_monthly');
            tests.push('✅ Return URLs configured');
            tests.push('✅ Webhook endpoints ready');
            
            showResult('env-config-result', tests.join('\n'), 'success');
        }

        function toggleSubscription() {
            showLoading('management-result');
            
            const isCurrentlySubscribed = checkSubscriptionStatus();
            const newStatus = !isCurrentlySubscribed;
            
            // Simulate subscription toggle
            const subscriptionData = {
                state: {
                    isSubscribed: newStatus,
                    subscriptionId: newStatus ? `demo_sub_${Date.now()}` : null,
                    subscriptionData: newStatus ? {
                        plan: 'fastcodr_premium_monthly',
                        status: 'active',
                        created: new Date().toISOString()
                    } : null
                }
            };
            
            localStorage.setItem('subscription-storage', JSON.stringify(subscriptionData));
            
            const tests = [];
            tests.push('=== SUBSCRIPTION MANAGEMENT ===');
            tests.push(`Status changed: ${isCurrentlySubscribed ? 'Premium' : 'Free'} → ${newStatus ? 'Premium' : 'Free'}`);
            tests.push(`Subscription ID: ${subscriptionData.state.subscriptionId || 'None'}`);
            tests.push('');
            tests.push('✅ Subscription status updated successfully');
            tests.push('💡 Refresh the page to see changes in other tests');
            
            showResult('management-result', tests.join('\n'), 'success');
        }

        function clearSubscriptionData() {
            localStorage.removeItem('subscription-storage');
            localStorage.removeItem('subscription_id');
            localStorage.removeItem('pending_subscription_id');
            
            showResult('management-result', 
                '=== SUBSCRIPTION DATA CLEARED ===\n✅ All subscription data removed from localStorage\n💡 User is now in free tier', 
                'success'
            );
        }

        function checkSubscriptionStatus() {
            const subscriptionData = localStorage.getItem('subscription-storage');
            if (subscriptionData) {
                try {
                    const data = JSON.parse(subscriptionData);
                    return data.state?.isSubscribed || false;
                } catch (e) {
                    return false;
                }
            }
            return false;
        }

        // Auto-run subscription status check on page load
        window.addEventListener('load', () => {
            setTimeout(testSubscriptionStatus, 1000);
        });
    </script>
</body>
</html>
